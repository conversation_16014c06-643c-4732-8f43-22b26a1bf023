#!/usr/bin/env python3
"""
Advanced Multi-Model Ensemble Voting System
Combines multiple AI models for superior decision making
"""

import asyncio
import concurrent.futures
import time
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from collections import defaultdict, Counter

# Import optimized components
from optimized_model_caller import OptimizedModelCaller
from memory_manager import MemoryManager

class VotingMethod(Enum):
    MAJORITY = "majority"
    WEIGHTED = "weighted"
    CONFIDENCE_WEIGHTED = "confidence_weighted"
    PERFORMANCE_WEIGHTED = "performance_weighted"
    ADAPTIVE = "adaptive"

@dataclass
class ModelPrediction:
    model_name: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    entry_price: Optional[float]
    target_price: Optional[float]
    stop_loss: Optional[float]
    reasoning: str
    response_time: float
    timestamp: datetime

@dataclass
class EnsembleDecision:
    final_action: str
    ensemble_confidence: float
    individual_predictions: List[ModelPrediction]
    voting_method: VotingMethod
    consensus_level: float
    conflicting_signals: bool
    execution_time: float
    timestamp: datetime

@dataclass
class ModelPerformance:
    model_name: str
    total_predictions: int
    correct_predictions: int
    accuracy: float
    avg_confidence: float
    avg_response_time: float
    profit_contribution: float
    recent_performance: float  # Last 10 trades
    weight: float

class AdvancedEnsembleVoting:
    """Advanced ensemble voting system with multiple strategies"""
    
    def __init__(self):
        self.model_caller = OptimizedModelCaller()
        self.memory_manager = MemoryManager()
        
        # Your enhanced models with performance weights
        self.models = {
            'analytical': {
                'name': 'phase2-smart-unrestricted-qwen3-14b-latest',
                'weight': 1.0,
                'specialty': 'complex_analysis',
                'avg_response_time': 41.4,
                'quality_score': 10
            },
            'finance_expert': {
                'name': 'phase2-unrestricted-noryon-qwen3-finance-v2-latest',
                'weight': 1.0,
                'specialty': 'finance_trading',
                'avg_response_time': 33.9,
                'quality_score': 10
            },
            'fast_trader': {
                'name': 'phase2-unrestricted-noryon-phi-4-9b-finance-latest',
                'weight': 0.9,
                'specialty': 'quick_decisions',
                'avg_response_time': 20.2,
                'quality_score': 10
            },
            'speed_optimized': {
                'name': 'speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest',
                'weight': 0.8,
                'specialty': 'rapid_response',
                'avg_response_time': 19.5,
                'quality_score': 10
            },
            'ultimate': {
                'name': 'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest',
                'weight': 0.9,
                'specialty': 'balanced_performance',
                'avg_response_time': 20.0,
                'quality_score': 8
            },
            'gpu_optimizer': {
                'name': 'kernelllm',
                'weight': 0.8,
                'specialty': 'gpu_optimization',
                'avg_response_time': 15.3,
                'quality_score': 9.5,
                'model_type': 'transformers',
                'capabilities': ['triton_kernels', 'gpu_acceleration', 'performance_optimization'],
                'trading_applications': ['technical_indicators', 'portfolio_calculations', 'risk_metrics']
            }
        }
        
        # Performance tracking
        self.model_performance = {}
        self.prediction_history = []
        self.ensemble_decisions = []
        
        # Initialize performance tracking
        for model_id, model_info in self.models.items():
            self.model_performance[model_id] = ModelPerformance(
                model_name=model_info['name'],
                total_predictions=0,
                correct_predictions=0,
                accuracy=0.8,  # Initial assumption
                avg_confidence=0.7,
                avg_response_time=model_info['avg_response_time'],
                profit_contribution=0.0,
                recent_performance=0.8,
                weight=model_info['weight']
            )
        
        # Voting configuration
        self.voting_config = {
            'min_models_required': 3,
            'max_response_time': 60,
            'confidence_threshold': 0.6,
            'consensus_threshold': 0.7,
            'adaptive_weight_decay': 0.95,
            'performance_window': 20  # Last N predictions for recent performance
        }
        
        print("🧠 Advanced Ensemble Voting System initialized")
        print(f"   Models: {len(self.models)} available")
        print(f"   Voting methods: {len(VotingMethod)} strategies")
        print(f"   Performance tracking: Active")
    
    async def get_ensemble_prediction(self, symbol: str, market_context: Dict[str, Any] = None, 
                                    voting_method: VotingMethod = VotingMethod.ADAPTIVE) -> EnsembleDecision:
        """Get ensemble prediction from multiple models"""
        start_time = time.time()
        
        print(f"\n🧠 Ensemble Analysis: {symbol}")
        print(f"   Voting method: {voting_method.value}")
        print(f"   Models querying: {len(self.models)}")
        
        # Generate prompt with market context
        prompt = self._create_enhanced_prompt(symbol, market_context)
        
        # Get predictions from all models in parallel
        predictions = await self._get_parallel_predictions(prompt, symbol)
        
        # Filter successful predictions
        valid_predictions = [p for p in predictions if p is not None]
        
        if len(valid_predictions) < self.voting_config['min_models_required']:
            print(f"   ⚠️ Insufficient models responded: {len(valid_predictions)}")
            return self._create_fallback_decision(symbol, valid_predictions, start_time)
        
        # Apply voting method
        ensemble_decision = self._apply_voting_method(valid_predictions, voting_method, start_time)
        
        # Update performance tracking
        self._update_model_performance(valid_predictions)
        
        # Store decision
        self.ensemble_decisions.append(ensemble_decision)
        
        print(f"   ✅ Ensemble decision: {ensemble_decision.final_action}")
        print(f"   📊 Confidence: {ensemble_decision.ensemble_confidence:.2f}")
        print(f"   🤝 Consensus: {ensemble_decision.consensus_level:.2f}")
        print(f"   ⏱️ Execution time: {ensemble_decision.execution_time:.1f}s")
        
        return ensemble_decision
    
    async def _get_parallel_predictions(self, prompt: str, symbol: str) -> List[Optional[ModelPrediction]]:
        """Get predictions from all models in parallel"""
        
        async def query_model(model_id: str, model_info: Dict) -> Optional[ModelPrediction]:
            """Query a single model asynchronously"""
            try:
                loop = asyncio.get_event_loop()
                
                # Run model call in thread pool to avoid blocking
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = loop.run_in_executor(
                        executor,
                        self.model_caller.call_model_safe,
                        model_info['name'],
                        prompt,
                        45  # timeout
                    )
                    
                    success, response, response_time = await future
                
                if success:
                    prediction = self._parse_model_response(
                        model_info['name'], response, response_time, symbol
                    )
                    if prediction:
                        print(f"   ✅ {model_id}: {prediction.action} (conf: {prediction.confidence:.2f}, {response_time:.1f}s)")
                        return prediction
                    else:
                        print(f"   ❌ {model_id}: Failed to parse response")
                else:
                    print(f"   ❌ {model_id}: {response}")
                
            except Exception as e:
                print(f"   ❌ {model_id}: Exception - {str(e)[:50]}")
            
            return None
        
        # Create tasks for all models
        tasks = [
            query_model(model_id, model_info) 
            for model_id, model_info in self.models.items()
        ]
        
        # Execute all tasks in parallel
        predictions = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        valid_predictions = [
            p for p in predictions 
            if isinstance(p, ModelPrediction)
        ]
        
        return valid_predictions
    
    def _create_enhanced_prompt(self, symbol: str, market_context: Dict[str, Any] = None) -> str:
        """Create enhanced prompt with market context"""
        base_prompt = f"""Analyze {symbol} and provide a precise trading signal.

REQUIRED FORMAT:
Action: BUY/SELL/HOLD
Confidence: X/10
Entry Price: $X.XX
Target Price: $X.XX
Stop Loss: $X.XX
Reasoning: Brief explanation

ANALYSIS REQUIREMENTS:
1. Technical analysis (trends, support/resistance, indicators)
2. Market sentiment and momentum
3. Risk/reward ratio assessment
4. Specific entry, target, and stop levels
5. Position sizing considerations

"""
        
        if market_context:
            base_prompt += f"\nMARKET CONTEXT:\n"
            for key, value in market_context.items():
                base_prompt += f"- {key}: {value}\n"
        
        base_prompt += f"\nProvide actionable, specific recommendations with exact price levels."
        
        return base_prompt
    
    def _parse_model_response(self, model_name: str, response: str, response_time: float, symbol: str) -> Optional[ModelPrediction]:
        """Parse model response into structured prediction"""
        try:
            response_lower = response.lower()
            
            # Extract action
            if 'action: buy' in response_lower or ('buy' in response_lower and 'don\'t buy' not in response_lower):
                action = 'BUY'
            elif 'action: sell' in response_lower or ('sell' in response_lower and 'don\'t sell' not in response_lower):
                action = 'SELL'
            else:
                action = 'HOLD'
            
            # Extract confidence
            confidence = 0.7  # Default
            import re
            conf_patterns = [
                r'confidence[:\s]+(\d+)[/\s]*10',
                r'(\d+)[/\s]*10\s*confidence',
                r'confidence[:\s]+(\d+)%'
            ]
            
            for pattern in conf_patterns:
                match = re.search(pattern, response_lower)
                if match:
                    conf_value = int(match.group(1))
                    confidence = conf_value / 10 if conf_value <= 10 else conf_value / 100
                    break
            
            # Extract prices
            price_patterns = [
                r'entry[:\s]+\$?(\d+\.?\d*)',
                r'target[:\s]+\$?(\d+\.?\d*)',
                r'stop[:\s]+\$?(\d+\.?\d*)',
                r'\$(\d+\.?\d*)'
            ]
            
            prices = []
            for pattern in price_patterns:
                matches = re.findall(pattern, response)
                prices.extend([float(m) for m in matches if m])
            
            entry_price = prices[0] if len(prices) >= 1 else None
            target_price = prices[1] if len(prices) >= 2 else None
            stop_loss = prices[2] if len(prices) >= 3 else None
            
            return ModelPrediction(
                model_name=model_name,
                action=action,
                confidence=confidence,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                reasoning=response[:200] + "..." if len(response) > 200 else response,
                response_time=response_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            print(f"Error parsing response from {model_name}: {e}")
            return None
    
    def _apply_voting_method(self, predictions: List[ModelPrediction], 
                           voting_method: VotingMethod, start_time: float) -> EnsembleDecision:
        """Apply the specified voting method"""
        
        if voting_method == VotingMethod.MAJORITY:
            return self._majority_voting(predictions, start_time)
        elif voting_method == VotingMethod.WEIGHTED:
            return self._weighted_voting(predictions, start_time)
        elif voting_method == VotingMethod.CONFIDENCE_WEIGHTED:
            return self._confidence_weighted_voting(predictions, start_time)
        elif voting_method == VotingMethod.PERFORMANCE_WEIGHTED:
            return self._performance_weighted_voting(predictions, start_time)
        elif voting_method == VotingMethod.ADAPTIVE:
            return self._adaptive_voting(predictions, start_time)
        else:
            return self._majority_voting(predictions, start_time)
    
    def _majority_voting(self, predictions: List[ModelPrediction], start_time: float) -> EnsembleDecision:
        """Simple majority voting"""
        actions = [p.action for p in predictions]
        action_counts = Counter(actions)
        final_action = action_counts.most_common(1)[0][0]
        
        # Calculate consensus level
        consensus_level = action_counts[final_action] / len(predictions)
        
        # Average confidence of models that voted for winning action
        winning_predictions = [p for p in predictions if p.action == final_action]
        ensemble_confidence = np.mean([p.confidence for p in winning_predictions])
        
        return EnsembleDecision(
            final_action=final_action,
            ensemble_confidence=ensemble_confidence,
            individual_predictions=predictions,
            voting_method=VotingMethod.MAJORITY,
            consensus_level=consensus_level,
            conflicting_signals=len(action_counts) > 1,
            execution_time=time.time() - start_time,
            timestamp=datetime.now()
        )
    
    def _confidence_weighted_voting(self, predictions: List[ModelPrediction], start_time: float) -> EnsembleDecision:
        """Confidence-weighted voting"""
        action_weights = defaultdict(float)
        total_confidence = 0
        
        for pred in predictions:
            action_weights[pred.action] += pred.confidence
            total_confidence += pred.confidence
        
        # Normalize weights
        for action in action_weights:
            action_weights[action] /= total_confidence
        
        # Find action with highest weighted score
        final_action = max(action_weights.items(), key=lambda x: x[1])[0]
        ensemble_confidence = action_weights[final_action]
        
        # Calculate consensus
        consensus_level = action_weights[final_action]
        
        return EnsembleDecision(
            final_action=final_action,
            ensemble_confidence=ensemble_confidence,
            individual_predictions=predictions,
            voting_method=VotingMethod.CONFIDENCE_WEIGHTED,
            consensus_level=consensus_level,
            conflicting_signals=len(action_weights) > 1,
            execution_time=time.time() - start_time,
            timestamp=datetime.now()
        )
    
    def _performance_weighted_voting(self, predictions: List[ModelPrediction], start_time: float) -> EnsembleDecision:
        """Performance-weighted voting based on historical accuracy"""
        action_weights = defaultdict(float)
        total_weight = 0
        
        for pred in predictions:
            # Find model performance
            model_perf = None
            for perf in self.model_performance.values():
                if perf.model_name == pred.model_name:
                    model_perf = perf
                    break
            
            if model_perf:
                weight = model_perf.recent_performance * pred.confidence
                action_weights[pred.action] += weight
                total_weight += weight
        
        if total_weight == 0:
            return self._majority_voting(predictions, start_time)
        
        # Normalize weights
        for action in action_weights:
            action_weights[action] /= total_weight
        
        final_action = max(action_weights.items(), key=lambda x: x[1])[0]
        ensemble_confidence = action_weights[final_action]
        consensus_level = action_weights[final_action]
        
        return EnsembleDecision(
            final_action=final_action,
            ensemble_confidence=ensemble_confidence,
            individual_predictions=predictions,
            voting_method=VotingMethod.PERFORMANCE_WEIGHTED,
            consensus_level=consensus_level,
            conflicting_signals=len(action_weights) > 1,
            execution_time=time.time() - start_time,
            timestamp=datetime.now()
        )
    
    def _adaptive_voting(self, predictions: List[ModelPrediction], start_time: float) -> EnsembleDecision:
        """Adaptive voting that combines multiple methods"""
        # Use performance weighting if we have enough history
        if len(self.ensemble_decisions) > 10:
            return self._performance_weighted_voting(predictions, start_time)
        else:
            # Fall back to confidence weighting for new systems
            return self._confidence_weighted_voting(predictions, start_time)
    
    def _create_fallback_decision(self, symbol: str, predictions: List[ModelPrediction], start_time: float) -> EnsembleDecision:
        """Create fallback decision when insufficient models respond"""
        if predictions:
            # Use the best available prediction
            best_pred = max(predictions, key=lambda p: p.confidence)
            return EnsembleDecision(
                final_action=best_pred.action,
                ensemble_confidence=best_pred.confidence * 0.5,  # Reduce confidence due to lack of consensus
                individual_predictions=predictions,
                voting_method=VotingMethod.MAJORITY,
                consensus_level=0.5,
                conflicting_signals=False,
                execution_time=time.time() - start_time,
                timestamp=datetime.now()
            )
        else:
            # No predictions available
            return EnsembleDecision(
                final_action='HOLD',
                ensemble_confidence=0.0,
                individual_predictions=[],
                voting_method=VotingMethod.MAJORITY,
                consensus_level=0.0,
                conflicting_signals=False,
                execution_time=time.time() - start_time,
                timestamp=datetime.now()
            )
    
    def _update_model_performance(self, predictions: List[ModelPrediction]):
        """Update model performance tracking"""
        for pred in predictions:
            # Find corresponding performance tracker
            for model_id, perf in self.model_performance.items():
                if perf.model_name == pred.model_name:
                    perf.total_predictions += 1
                    perf.avg_response_time = (
                        perf.avg_response_time * 0.9 + pred.response_time * 0.1
                    )
                    perf.avg_confidence = (
                        perf.avg_confidence * 0.9 + pred.confidence * 0.1
                    )
                    break
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        return {
            'timestamp': datetime.now().isoformat(),
            'total_ensemble_decisions': len(self.ensemble_decisions),
            'model_performance': {
                model_id: asdict(perf) 
                for model_id, perf in self.model_performance.items()
            },
            'recent_decisions': [
                asdict(decision) for decision in self.ensemble_decisions[-5:]
            ],
            'voting_method_usage': Counter([
                d.voting_method.value for d in self.ensemble_decisions
            ]),
            'average_consensus': np.mean([
                d.consensus_level for d in self.ensemble_decisions
            ]) if self.ensemble_decisions else 0,
            'average_execution_time': np.mean([
                d.execution_time for d in self.ensemble_decisions
            ]) if self.ensemble_decisions else 0
        }

async def main():
    """Test the advanced ensemble voting system"""
    print("ADVANCED ENSEMBLE VOTING SYSTEM - PHASE 3B")
    print("=" * 60)
    
    # Initialize ensemble
    ensemble = AdvancedEnsembleVoting()
    
    # Test symbols
    test_symbols = ["BTC", "ETH"]
    
    for symbol in test_symbols:
        print(f"\n🧪 Testing ensemble prediction for {symbol}...")
        
        # Market context
        market_context = {
            "market_trend": "bullish",
            "volatility": "medium",
            "volume": "high",
            "news_sentiment": "positive"
        }
        
        # Get ensemble decision
        decision = await ensemble.get_ensemble_prediction(
            symbol, market_context, VotingMethod.ADAPTIVE
        )
        
        print(f"\n📊 ENSEMBLE RESULT:")
        print(f"   Action: {decision.final_action}")
        print(f"   Confidence: {decision.ensemble_confidence:.2f}")
        print(f"   Consensus: {decision.consensus_level:.2f}")
        print(f"   Models used: {len(decision.individual_predictions)}")
        print(f"   Conflicting signals: {decision.conflicting_signals}")
    
    # Performance report
    print(f"\n📈 PERFORMANCE REPORT:")
    report = ensemble.get_performance_report()
    print(f"   Total decisions: {report['total_ensemble_decisions']}")
    print(f"   Average consensus: {report['average_consensus']:.2f}")
    print(f"   Average execution time: {report['average_execution_time']:.1f}s")
    
    print(f"\n✅ Advanced Ensemble Voting System operational!")

if __name__ == "__main__":
    asyncio.run(main())
