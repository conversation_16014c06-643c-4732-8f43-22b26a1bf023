#!/usr/bin/env python3
"""
SYSTEM STATUS DASHBOARD
Real-time monitoring dashboard for the Noryon AI Trading System
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3
import socket
import urllib.request
from typing import Dict, List, Any

class SystemStatusDashboard:
    """Real-time system status dashboard"""
    
    def __init__(self):
        self.status_data = {}
        self.last_update = None
        
    async def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        print("🔍 GATHERING COMPREHENSIVE SYSTEM STATUS")
        print("=" * 50)
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'system_health': await self._get_system_health(),
            'api_server': await self._get_api_server_status(),
            'databases': await self._get_database_status(),
            'ai_models': await self._get_ai_model_status(),
            'network': await self._get_network_status(),
            'performance': await self._get_performance_status(),
            'security': await self._get_security_status(),
            'overall_score': 0
        }
        
        # Calculate overall health score
        status['overall_score'] = self._calculate_overall_score(status)
        
        return status
    
    async def _get_system_health(self) -> Dict[str, Any]:
        """Get system health information"""
        try:
            import psutil
            
            # CPU and memory info
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            return {
                'status': 'healthy',
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': (disk.used / disk.total) * 100,
                'available_memory_gb': memory.available // (1024**3),
                'free_disk_gb': disk.free // (1024**3),
                'uptime': time.time() - psutil.boot_time()
            }
        except ImportError:
            return {
                'status': 'limited',
                'message': 'psutil not available - limited system monitoring'
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _get_api_server_status(self) -> Dict[str, Any]:
        """Get API server status"""
        try:
            # Test API server connectivity
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', 8000))
            sock.close()
            
            if result == 0:
                # Test health endpoint
                try:
                    req = urllib.request.Request("http://localhost:8000/health")
                    with urllib.request.urlopen(req, timeout=10) as response:
                        if response.status == 200:
                            health_data = json.loads(response.read().decode())
                            return {
                                'status': 'running',
                                'port': 8000,
                                'health_check': 'passed',
                                'uptime': health_data.get('uptime', 0),
                                'services': health_data.get('services', {})
                            }
                        else:
                            return {
                                'status': 'running',
                                'port': 8000,
                                'health_check': 'failed',
                                'status_code': response.status
                            }
                except Exception as e:
                    return {
                        'status': 'running',
                        'port': 8000,
                        'health_check': 'error',
                        'error': str(e)
                    }
            else:
                return {
                    'status': 'offline',
                    'port': 8000,
                    'error': 'Connection refused'
                }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _get_database_status(self) -> Dict[str, Any]:
        """Get database status"""
        try:
            db_files = list(Path('.').glob('*.db'))
            databases = []
            total_size = 0
            accessible_count = 0
            
            for db_file in db_files:
                try:
                    size = db_file.stat().st_size
                    total_size += size
                    
                    # Test connectivity
                    conn = sqlite3.connect(str(db_file))
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                    table_count = cursor.fetchone()[0]
                    conn.close()
                    
                    accessible_count += 1
                    databases.append({
                        'name': db_file.name,
                        'size_mb': size / (1024 * 1024),
                        'tables': table_count,
                        'status': 'accessible'
                    })
                except Exception as e:
                    databases.append({
                        'name': db_file.name,
                        'size_mb': 0,
                        'tables': 0,
                        'status': 'error',
                        'error': str(e)
                    })
            
            return {
                'status': 'healthy' if accessible_count == len(db_files) else 'warning',
                'total_databases': len(db_files),
                'accessible_databases': accessible_count,
                'total_size_mb': total_size / (1024 * 1024),
                'databases': databases[:10]  # Show first 10
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _get_ai_model_status(self) -> Dict[str, Any]:
        """Get AI model status"""
        try:
            # Check Ollama service
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', 11434))
            sock.close()
            
            if result == 0:
                try:
                    # Get model list
                    req = urllib.request.Request("http://localhost:11434/api/tags")
                    with urllib.request.urlopen(req, timeout=10) as response:
                        if response.status == 200:
                            data = json.loads(response.read().decode())
                            models = data.get('models', [])
                            
                            # Count model configuration files
                            model_files = list(Path('.').glob('Modelfile.*'))
                            
                            return {
                                'status': 'healthy',
                                'ollama_running': True,
                                'total_models': len(models),
                                'model_files': len(model_files),
                                'models': [m.get('name', 'Unknown') for m in models[:10]]
                            }
                        else:
                            return {
                                'status': 'warning',
                                'ollama_running': True,
                                'api_status': response.status,
                                'error': 'API not responding properly'
                            }
                except Exception as e:
                    return {
                        'status': 'warning',
                        'ollama_running': True,
                        'api_error': str(e)
                    }
            else:
                return {
                    'status': 'critical',
                    'ollama_running': False,
                    'error': 'Ollama service not running'
                }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _get_network_status(self) -> Dict[str, Any]:
        """Get network connectivity status"""
        test_hosts = [
            ('google.com', 80, 'Internet'),
            ('api.binance.com', 443, 'Crypto Data'),
            ('localhost', 11434, 'Ollama'),
            ('localhost', 8000, 'API Server')
        ]
        
        connectivity = []
        successful_connections = 0
        
        for host, port, description in test_hosts:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                start_time = time.time()
                result = sock.connect_ex((host, port))
                response_time = time.time() - start_time
                sock.close()
                
                if result == 0:
                    successful_connections += 1
                    connectivity.append({
                        'host': host,
                        'port': port,
                        'description': description,
                        'status': 'connected',
                        'response_time': response_time
                    })
                else:
                    connectivity.append({
                        'host': host,
                        'port': port,
                        'description': description,
                        'status': 'failed',
                        'response_time': 0
                    })
            except Exception as e:
                connectivity.append({
                    'host': host,
                    'port': port,
                    'description': description,
                    'status': 'error',
                    'error': str(e)
                })
        
        return {
            'status': 'healthy' if successful_connections == len(test_hosts) else 'warning',
            'successful_connections': successful_connections,
            'total_tests': len(test_hosts),
            'connectivity': connectivity
        }
    
    async def _get_performance_status(self) -> Dict[str, Any]:
        """Get performance status"""
        try:
            # Test API response time
            start_time = time.time()
            try:
                req = urllib.request.Request("http://localhost:8000/health")
                with urllib.request.urlopen(req, timeout=10) as response:
                    api_response_time = time.time() - start_time
                    api_status = 'healthy' if api_response_time < 2.0 else 'warning'
            except:
                api_response_time = 0
                api_status = 'error'
            
            # Check Python version
            import sys
            python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            python_status = 'healthy' if sys.version_info >= (3, 8) else 'warning'
            
            return {
                'status': 'healthy' if api_status == 'healthy' and python_status == 'healthy' else 'warning',
                'api_response_time': api_response_time,
                'python_version': python_version,
                'python_status': python_status,
                'api_status': api_status
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _get_security_status(self) -> Dict[str, Any]:
        """Get security status"""
        try:
            security_checks = []
            issues_found = 0
            
            # Check file permissions
            sensitive_files = ['.env', 'config.yaml']
            for file_path in sensitive_files:
                if Path(file_path).exists():
                    try:
                        import os
                        stat_info = os.stat(file_path)
                        permissions = oct(stat_info.st_mode)[-3:]
                        
                        if permissions in ['600', '700']:
                            security_checks.append({
                                'check': f'File permissions: {file_path}',
                                'status': 'secure',
                                'details': f'Permissions: {permissions}'
                            })
                        else:
                            issues_found += 1
                            security_checks.append({
                                'check': f'File permissions: {file_path}',
                                'status': 'warning',
                                'details': f'Insecure permissions: {permissions}'
                            })
                    except Exception as e:
                        issues_found += 1
                        security_checks.append({
                            'check': f'File permissions: {file_path}',
                            'status': 'error',
                            'details': str(e)
                        })
            
            # Check for backup files
            backup_files = []
            for pattern in ['*.bak', '*.backup', '*.old']:
                backup_files.extend(list(Path('.').glob(pattern)))
            
            if backup_files:
                issues_found += 1
                security_checks.append({
                    'check': 'Backup files',
                    'status': 'warning',
                    'details': f'Found {len(backup_files)} backup files'
                })
            else:
                security_checks.append({
                    'check': 'Backup files',
                    'status': 'secure',
                    'details': 'No insecure backup files found'
                })
            
            return {
                'status': 'secure' if issues_found == 0 else 'warning',
                'issues_found': issues_found,
                'security_checks': security_checks
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _calculate_overall_score(self, status: Dict[str, Any]) -> float:
        """Calculate overall system health score"""
        scores = []
        
        # System health score
        if status['system_health']['status'] == 'healthy':
            scores.append(100)
        elif status['system_health']['status'] == 'limited':
            scores.append(80)
        else:
            scores.append(50)
        
        # API server score
        if status['api_server']['status'] == 'running' and status['api_server'].get('health_check') == 'passed':
            scores.append(100)
        elif status['api_server']['status'] == 'running':
            scores.append(80)
        else:
            scores.append(0)
        
        # Database score
        if status['databases']['status'] == 'healthy':
            scores.append(100)
        elif status['databases']['status'] == 'warning':
            scores.append(70)
        else:
            scores.append(30)
        
        # AI models score
        if status['ai_models']['status'] == 'healthy':
            scores.append(100)
        elif status['ai_models']['status'] == 'warning':
            scores.append(60)
        else:
            scores.append(20)
        
        # Network score
        if status['network']['status'] == 'healthy':
            scores.append(100)
        else:
            scores.append(70)
        
        # Performance score
        if status['performance']['status'] == 'healthy':
            scores.append(100)
        else:
            scores.append(80)
        
        # Security score
        if status['security']['status'] == 'secure':
            scores.append(100)
        elif status['security']['status'] == 'warning':
            scores.append(75)
        else:
            scores.append(50)
        
        return sum(scores) / len(scores) if scores else 0
    
    def print_status_dashboard(self, status: Dict[str, Any]):
        """Print formatted status dashboard"""
        print(f"\n🎯 NORYON AI TRADING SYSTEM - STATUS DASHBOARD")
        print("=" * 70)
        print(f"📅 Timestamp: {status['timestamp']}")
        print(f"🎯 Overall Health Score: {status['overall_score']:.1f}/100")
        
        # System Health
        sys_health = status['system_health']
        if sys_health['status'] == 'healthy':
            print(f"\n💻 SYSTEM HEALTH: ✅ HEALTHY")
            print(f"   CPU Usage: {sys_health['cpu_usage']:.1f}%")
            print(f"   Memory Usage: {sys_health['memory_usage']:.1f}%")
            print(f"   Disk Usage: {sys_health['disk_usage']:.1f}%")
            print(f"   Available Memory: {sys_health['available_memory_gb']}GB")
            print(f"   Free Disk Space: {sys_health['free_disk_gb']}GB")
        else:
            print(f"\n💻 SYSTEM HEALTH: ⚠️ {sys_health['status'].upper()}")
        
        # API Server
        api_status = status['api_server']
        if api_status['status'] == 'running':
            health_icon = "✅" if api_status.get('health_check') == 'passed' else "⚠️"
            print(f"\n🌐 API SERVER: {health_icon} RUNNING")
            print(f"   Port: {api_status['port']}")
            print(f"   Health Check: {api_status.get('health_check', 'unknown')}")
            if 'uptime' in api_status:
                print(f"   Uptime: {api_status['uptime']:.1f} seconds")
        else:
            print(f"\n🌐 API SERVER: ❌ {api_status['status'].upper()}")
        
        # Databases
        db_status = status['databases']
        if db_status['status'] == 'healthy':
            print(f"\n💾 DATABASES: ✅ HEALTHY")
        else:
            print(f"\n💾 DATABASES: ⚠️ {db_status['status'].upper()}")
        print(f"   Total Databases: {db_status['total_databases']}")
        print(f"   Accessible: {db_status['accessible_databases']}")
        print(f"   Total Size: {db_status['total_size_mb']:.1f}MB")
        
        # AI Models
        ai_status = status['ai_models']
        if ai_status['status'] == 'healthy':
            print(f"\n🤖 AI MODELS: ✅ HEALTHY")
            print(f"   Ollama Running: {ai_status['ollama_running']}")
            print(f"   Total Models: {ai_status['total_models']}")
            print(f"   Model Files: {ai_status['model_files']}")
        else:
            print(f"\n🤖 AI MODELS: ❌ {ai_status['status'].upper()}")
        
        # Network
        net_status = status['network']
        if net_status['status'] == 'healthy':
            print(f"\n🌐 NETWORK: ✅ HEALTHY")
        else:
            print(f"\n🌐 NETWORK: ⚠️ {net_status['status'].upper()}")
        print(f"   Successful Connections: {net_status['successful_connections']}/{net_status['total_tests']}")
        
        # Performance
        perf_status = status['performance']
        if perf_status['status'] == 'healthy':
            print(f"\n⚡ PERFORMANCE: ✅ HEALTHY")
        else:
            print(f"\n⚡ PERFORMANCE: ⚠️ {perf_status['status'].upper()}")
        print(f"   API Response Time: {perf_status['api_response_time']:.3f}s")
        print(f"   Python Version: {perf_status['python_version']}")
        
        # Security
        sec_status = status['security']
        if sec_status['status'] == 'secure':
            print(f"\n🔒 SECURITY: ✅ SECURE")
        else:
            print(f"\n🔒 SECURITY: ⚠️ {sec_status['status'].upper()}")
        print(f"   Issues Found: {sec_status['issues_found']}")
        
        print("\n" + "=" * 70)

async def main():
    """Main dashboard function"""
    dashboard = SystemStatusDashboard()
    
    print("🔍 NORYON SYSTEM STATUS DASHBOARD")
    print("Gathering comprehensive system status...")
    
    status = await dashboard.get_comprehensive_status()
    dashboard.print_status_dashboard(status)
    
    # Save status to file
    status_file = Path("system_status.json")
    with open(status_file, 'w') as f:
        json.dump(status, f, indent=2, default=str)
    
    print(f"\n📄 Status saved to: {status_file}")
    
    return status

if __name__ == "__main__":
    asyncio.run(main())
