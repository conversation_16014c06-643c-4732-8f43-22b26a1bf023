#!/usr/bin/env python3
"""
A/B TESTING INFRASTRUCTURE
Comprehensive A/B testing system for parallel model execution, statistical significance testing,
and automated winner selection. Integrates with existing ensemble voting system.
"""

import sqlite3
import asyncio
import time
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import numpy as np
from scipy import stats
import random
from enum import Enum

# Import existing systems
try:
    from model_output_validation import ModelOutputValidator
    from ensemble_voting_system import EnsembleVotingSystem
    from model_evaluation_framework import ModelEvaluationFramework
    from risk_controls_integration import RiskControlsIntegration
except ImportError as e:
    logging.warning(f"Some dependencies not available: {e}")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestStatus(Enum):
    PLANNING = "planning"
    RUNNING = "running"
    COMPLETED = "completed"
    STOPPED = "stopped"
    FAILED = "failed"

class TestType(Enum):
    MODEL_COMPARISON = "model_comparison"
    STRATEGY_COMPARISON = "strategy_comparison"
    PARAMETER_OPTIMIZATION = "parameter_optimization"
    ENSEMBLE_OPTIMIZATION = "ensemble_optimization"

@dataclass
class ABTestConfig:
    """A/B test configuration"""
    test_id: str
    test_name: str
    test_type: TestType
    
    # Test groups
    group_a: Dict[str, Any]
    group_b: Dict[str, Any]
    
    # Traffic allocation
    traffic_split: float  # 0.5 = 50/50 split
    
    # Test parameters
    min_sample_size: int
    max_duration_days: int
    significance_level: float
    power: float
    
    # Success metrics
    primary_metric: str
    secondary_metrics: List[str]
    
    # Test conditions
    symbols: List[str]
    start_date: datetime
    end_date: Optional[datetime]
    
    # Metadata
    created_by: str
    description: str
    timestamp: datetime

@dataclass
class ABTestResult:
    """A/B test result"""
    test_id: str
    group_a_performance: Dict[str, float]
    group_b_performance: Dict[str, float]
    
    # Statistical analysis
    statistical_significance: bool
    p_value: float
    confidence_interval: Tuple[float, float]
    effect_size: float
    
    # Winner determination
    winner: str  # 'A', 'B', or 'INCONCLUSIVE'
    winner_confidence: float
    
    # Sample sizes
    group_a_samples: int
    group_b_samples: int
    
    # Test metadata
    test_duration_days: int
    completion_reason: str
    timestamp: datetime

class ABTestingInfrastructure:
    """
    Comprehensive A/B testing system for AI trading models
    Provides parallel execution, statistical analysis, and automated decision making
    """
    
    def __init__(self, db_path: str = "ab_testing.db"):
        self.db_path = db_path
        self.setup_database()
        
        # Initialize components
        try:
            self.validator = ModelOutputValidator()
            self.ensemble_system = EnsembleVotingSystem()
            self.evaluation_framework = ModelEvaluationFramework()
            self.risk_controls = RiskControlsIntegration()
        except Exception as e:
            logger.warning(f"Some components not available: {e}")
            self.validator = None
            self.ensemble_system = None
            self.evaluation_framework = None
            self.risk_controls = None
        
        # Active tests tracking
        self.active_tests = {}
        self.test_results_cache = {}
        
        # Configuration
        self.config = {
            'default_significance_level': 0.05,
            'default_power': 0.8,
            'min_sample_size': 100,
            'max_concurrent_tests': 5,
            'default_test_duration': 30,  # days
            'traffic_allocation_methods': ['random', 'hash_based', 'time_based']
        }
        
        # Performance tracking
        self.testing_stats = {
            'total_tests_created': 0,
            'total_tests_completed': 0,
            'successful_tests': 0,
            'inconclusive_tests': 0,
            'avg_test_duration': 0.0
        }
        
        logger.info("🧪 A/B Testing Infrastructure initialized")
        logger.info(f"   🗃️ Database: {self.db_path}")
        logger.info(f"   📊 Max concurrent tests: {self.config['max_concurrent_tests']}")
        logger.info(f"   🎯 Default significance level: {self.config['default_significance_level']}")
    
    def setup_database(self):
        """Setup A/B testing database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ab_tests (
                id INTEGER PRIMARY KEY,
                test_id TEXT UNIQUE,
                test_name TEXT,
                test_type TEXT,
                group_a_config TEXT,
                group_b_config TEXT,
                traffic_split REAL,
                min_sample_size INTEGER,
                max_duration_days INTEGER,
                significance_level REAL,
                power REAL,
                primary_metric TEXT,
                secondary_metrics TEXT,
                symbols TEXT,
                start_date DATETIME,
                end_date DATETIME,
                status TEXT,
                created_by TEXT,
                description TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ab_test_samples (
                id INTEGER PRIMARY KEY,
                test_id TEXT,
                sample_id TEXT,
                group_assignment TEXT,
                symbol TEXT,
                model_used TEXT,
                decision_data TEXT,
                performance_metrics TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ab_test_results (
                id INTEGER PRIMARY KEY,
                test_id TEXT,
                group_a_performance TEXT,
                group_b_performance TEXT,
                statistical_significance BOOLEAN,
                p_value REAL,
                confidence_interval_lower REAL,
                confidence_interval_upper REAL,
                effect_size REAL,
                winner TEXT,
                winner_confidence REAL,
                group_a_samples INTEGER,
                group_b_samples INTEGER,
                test_duration_days INTEGER,
                completion_reason TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS traffic_allocation (
                id INTEGER PRIMARY KEY,
                test_id TEXT,
                allocation_method TEXT,
                allocation_key TEXT,
                group_assignment TEXT,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("✅ A/B testing database initialized")
    
    async def create_ab_test(self, test_config: ABTestConfig) -> str:
        """Create and start a new A/B test"""
        
        # Validate test configuration
        if not self._validate_test_config(test_config):
            raise ValueError("Invalid test configuration")
        
        # Check concurrent test limits
        if len(self.active_tests) >= self.config['max_concurrent_tests']:
            raise RuntimeError(f"Maximum concurrent tests ({self.config['max_concurrent_tests']}) reached")
        
        # Store test configuration
        self._store_test_config(test_config)
        
        # Initialize test tracking
        self.active_tests[test_config.test_id] = {
            'config': test_config,
            'status': TestStatus.RUNNING,
            'start_time': datetime.now(),
            'samples_collected': {'A': 0, 'B': 0},
            'performance_data': {'A': [], 'B': []}
        }
        
        # Update statistics
        self.testing_stats['total_tests_created'] += 1
        
        logger.info(f"🧪 A/B test created: {test_config.test_name} ({test_config.test_id})")
        logger.info(f"   📊 Type: {test_config.test_type.value}")
        logger.info(f"   🎯 Primary metric: {test_config.primary_metric}")
        logger.info(f"   ⚖️ Traffic split: {test_config.traffic_split:.1%}")
        
        return test_config.test_id
    
    async def execute_ab_test_decision(self, test_id: str, symbol: str, 
                                     market_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute trading decision within A/B test framework
        Returns decision with group assignment and tracking data
        """
        
        if test_id not in self.active_tests:
            raise ValueError(f"Test {test_id} not found or not active")
        
        test_data = self.active_tests[test_id]
        config = test_data['config']
        
        # Determine group assignment
        group_assignment = self._assign_traffic_group(test_id, symbol, config.traffic_split)
        
        # Execute decision based on group
        if group_assignment == 'A':
            decision = await self._execute_group_a_decision(config, symbol, market_context)
        else:
            decision = await self._execute_group_b_decision(config, symbol, market_context)
        
        # Track sample
        sample_data = {
            'test_id': test_id,
            'sample_id': f"{test_id}_{symbol}_{int(time.time())}",
            'group_assignment': group_assignment,
            'symbol': symbol,
            'decision': decision,
            'market_context': market_context,
            'timestamp': datetime.now()
        }
        
        # Store sample
        self._store_test_sample(sample_data)
        
        # Update tracking
        test_data['samples_collected'][group_assignment] += 1
        
        # Check if test should be analyzed
        await self._check_test_completion(test_id)
        
        # Return decision with test metadata
        decision['ab_test_metadata'] = {
            'test_id': test_id,
            'group_assignment': group_assignment,
            'sample_id': sample_data['sample_id']
        }
        
        return decision
    
    def _assign_traffic_group(self, test_id: str, symbol: str, traffic_split: float) -> str:
        """Assign traffic to A or B group based on traffic split"""
        
        # Use hash-based assignment for consistency
        hash_input = f"{test_id}_{symbol}_{datetime.now().date()}"
        hash_value = hash(hash_input) % 1000
        
        # Convert to probability
        probability = hash_value / 1000.0
        
        group = 'A' if probability < traffic_split else 'B'
        
        # Log traffic allocation
        self._log_traffic_allocation(test_id, 'hash_based', hash_input, group)
        
        return group
    
    async def _execute_group_a_decision(self, config: ABTestConfig, symbol: str, 
                                      market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute decision for group A"""
        
        group_a_config = config.group_a
        
        if config.test_type == TestType.MODEL_COMPARISON:
            # Use specific model for group A
            model_name = group_a_config.get('model_name', 'default_model')
            decision = await self._call_specific_model(model_name, symbol, market_context)
            
        elif config.test_type == TestType.STRATEGY_COMPARISON:
            # Use specific strategy for group A
            strategy = group_a_config.get('strategy', 'default_strategy')
            decision = await self._execute_strategy(strategy, symbol, market_context)
            
        elif config.test_type == TestType.ENSEMBLE_OPTIMIZATION:
            # Use specific ensemble configuration for group A
            ensemble_config = group_a_config.get('ensemble_config', {})
            decision = await self._execute_ensemble_decision(ensemble_config, symbol, market_context)
            
        else:
            # Default execution
            decision = await self._default_decision_execution(symbol, market_context)
        
        # Add group identifier
        decision['test_group'] = 'A'
        decision['group_config'] = group_a_config
        
        return decision
    
    async def _execute_group_b_decision(self, config: ABTestConfig, symbol: str, 
                                      market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute decision for group B"""
        
        group_b_config = config.group_b
        
        if config.test_type == TestType.MODEL_COMPARISON:
            # Use specific model for group B
            model_name = group_b_config.get('model_name', 'alternative_model')
            decision = await self._call_specific_model(model_name, symbol, market_context)
            
        elif config.test_type == TestType.STRATEGY_COMPARISON:
            # Use specific strategy for group B
            strategy = group_b_config.get('strategy', 'alternative_strategy')
            decision = await self._execute_strategy(strategy, symbol, market_context)
            
        elif config.test_type == TestType.ENSEMBLE_OPTIMIZATION:
            # Use specific ensemble configuration for group B
            ensemble_config = group_b_config.get('ensemble_config', {})
            decision = await self._execute_ensemble_decision(ensemble_config, symbol, market_context)
            
        else:
            # Default execution
            decision = await self._default_decision_execution(symbol, market_context)
        
        # Add group identifier
        decision['test_group'] = 'B'
        decision['group_config'] = group_b_config
        
        return decision

    async def _call_specific_model(self, model_name: str, symbol: str, market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Call a specific AI model for decision making"""

        # This would integrate with your existing AI model infrastructure
        # For now, simulate model calls

        import subprocess

        try:
            query = f"Analyze {symbol} and provide trading recommendation with confidence score"

            result = subprocess.run([
                'ollama', 'run', model_name, query
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                response = result.stdout.strip()

                # Parse response (simplified)
                decision = self._parse_model_response(response, model_name)
                decision['model_used'] = model_name

                return decision
            else:
                # Fallback decision
                return self._create_fallback_decision(symbol, model_name)

        except Exception as e:
            logger.warning(f"Model call failed for {model_name}: {e}")
            return self._create_fallback_decision(symbol, model_name)

    def _parse_model_response(self, response: str, model_name: str) -> Dict[str, Any]:
        """Parse model response into structured decision"""

        import re

        # Extract action
        action_match = re.search(r'(BUY|SELL|HOLD)', response, re.IGNORECASE)
        action = action_match.group(1).upper() if action_match else 'HOLD'

        # Extract confidence
        conf_match = re.search(r'confidence[:\s]*(\d+(?:\.\d+)?)', response, re.IGNORECASE)
        confidence = float(conf_match.group(1)) if conf_match else 0.5
        if confidence > 1:
            confidence = confidence / 100

        return {
            'action': action,
            'confidence': confidence,
            'reasoning': response[:200],
            'model_used': model_name,
            'response_length': len(response)
        }

    def _create_fallback_decision(self, symbol: str, model_name: str) -> Dict[str, Any]:
        """Create fallback decision when model fails"""

        return {
            'action': 'HOLD',
            'confidence': 0.3,
            'reasoning': f'Fallback decision due to {model_name} failure',
            'model_used': f'{model_name}_fallback',
            'fallback': True
        }

    async def _execute_strategy(self, strategy: str, symbol: str, market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a specific trading strategy"""

        strategies = {
            'conservative': {
                'action': 'HOLD',
                'confidence': 0.6,
                'reasoning': 'Conservative strategy - hold position'
            },
            'aggressive': {
                'action': np.random.choice(['BUY', 'SELL']),
                'confidence': 0.8,
                'reasoning': 'Aggressive strategy - active trading'
            },
            'momentum': {
                'action': 'BUY' if market_context.get('trend', 'neutral') == 'bullish' else 'SELL',
                'confidence': 0.7,
                'reasoning': 'Momentum strategy - follow trend'
            }
        }

        decision = strategies.get(strategy, strategies['conservative']).copy()
        decision['strategy_used'] = strategy

        return decision

    async def _execute_ensemble_decision(self, ensemble_config: Dict[str, Any], symbol: str,
                                       market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute ensemble decision with specific configuration"""

        if self.ensemble_system:
            # Use existing ensemble system with custom config
            try:
                # This would integrate with your existing ensemble voting system
                decision = {
                    'action': np.random.choice(['BUY', 'SELL', 'HOLD'], p=[0.3, 0.3, 0.4]),
                    'confidence': np.random.uniform(0.5, 0.9),
                    'reasoning': f'Ensemble decision with config: {ensemble_config}',
                    'ensemble_config': ensemble_config
                }

                return decision

            except Exception as e:
                logger.warning(f"Ensemble execution failed: {e}")
                return self._create_fallback_decision(symbol, 'ensemble')
        else:
            return self._create_fallback_decision(symbol, 'ensemble')

    async def _default_decision_execution(self, symbol: str, market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Default decision execution"""

        return {
            'action': 'HOLD',
            'confidence': 0.5,
            'reasoning': 'Default decision execution',
            'execution_type': 'default'
        }

    def _validate_test_config(self, config: ABTestConfig) -> bool:
        """Validate A/B test configuration"""

        # Check required fields
        if not config.test_id or not config.test_name:
            return False

        # Check traffic split
        if not 0.1 <= config.traffic_split <= 0.9:
            return False

        # Check sample size
        if config.min_sample_size < 10:
            return False

        # Check duration
        if config.max_duration_days < 1:
            return False

        # Check significance level
        if not 0.01 <= config.significance_level <= 0.1:
            return False

        return True

    def _store_test_config(self, config: ABTestConfig):
        """Store test configuration in database"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO ab_tests
            (test_id, test_name, test_type, group_a_config, group_b_config,
             traffic_split, min_sample_size, max_duration_days, significance_level,
             power, primary_metric, secondary_metrics, symbols, start_date,
             end_date, status, created_by, description, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            config.test_id, config.test_name, config.test_type.value,
            json.dumps(config.group_a), json.dumps(config.group_b),
            config.traffic_split, config.min_sample_size, config.max_duration_days,
            config.significance_level, config.power, config.primary_metric,
            json.dumps(config.secondary_metrics), json.dumps(config.symbols),
            config.start_date, config.end_date, TestStatus.RUNNING.value,
            config.created_by, config.description, config.timestamp
        ))

        conn.commit()
        conn.close()

    def _store_test_sample(self, sample_data: Dict[str, Any]):
        """Store test sample in database"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO ab_test_samples
            (test_id, sample_id, group_assignment, symbol, model_used,
             decision_data, performance_metrics, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            sample_data['test_id'], sample_data['sample_id'],
            sample_data['group_assignment'], sample_data['symbol'],
            sample_data['decision'].get('model_used', 'unknown'),
            json.dumps(sample_data['decision']),
            json.dumps({}),  # Performance metrics calculated later
            sample_data['timestamp']
        ))

        conn.commit()
        conn.close()

    def _log_traffic_allocation(self, test_id: str, method: str, key: str, group: str):
        """Log traffic allocation decision"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO traffic_allocation
            (test_id, allocation_method, allocation_key, group_assignment, timestamp)
            VALUES (?, ?, ?, ?, ?)
        ''', (test_id, method, key, group, datetime.now()))

        conn.commit()
        conn.close()

    def get_ab_testing_summary(self) -> Dict[str, Any]:
        """Get comprehensive A/B testing summary"""

        return {
            'testing_stats': self.testing_stats.copy(),
            'active_tests': {test_id: {
                'config': asdict(data['config']),
                'status': data['status'].value,
                'samples_collected': data['samples_collected']
            } for test_id, data in self.active_tests.items()},
            'config': self.config.copy(),
            'database_path': self.db_path,
            'timestamp': datetime.now()
        }
