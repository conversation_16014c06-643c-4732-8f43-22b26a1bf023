#!/usr/bin/env python3
"""
System Recovery Manager
Handles component failures and maintains system uptime above 99%
"""

import time
import logging
import threading
import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import subprocess
import os
import signal

# Optional psutil import for enhanced monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComponentStatus(Enum):
    """Component status enumeration"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    RECOVERING = "recovering"
    UNKNOWN = "unknown"

class RecoveryAction(Enum):
    """Recovery action types"""
    RESTART = "restart"
    RESET = "reset"
    FAILOVER = "failover"
    ROLLBACK = "rollback"
    MANUAL_INTERVENTION = "manual_intervention"

@dataclass
class ComponentHealth:
    """Component health information"""
    component_name: str
    status: ComponentStatus
    last_check: datetime
    error_count: int
    recovery_attempts: int
    last_error: Optional[str] = None
    performance_metrics: Dict[str, Any] = None

@dataclass
class RecoveryPlan:
    """Recovery plan for a component"""
    component_name: str
    actions: List[RecoveryAction]
    timeout: int
    max_attempts: int
    dependencies: List[str] = None
    rollback_plan: Optional[str] = None

@dataclass
class SystemCheckpoint:
    """System state checkpoint"""
    timestamp: datetime
    component_states: Dict[str, Any]
    configuration_snapshot: Dict[str, Any]
    performance_baseline: Dict[str, float]
    checkpoint_id: str

class SystemRecoveryManager:
    """Manages system recovery and maintains high availability"""
    
    def __init__(self, config_path: str = "config/recovery_config.yaml"):
        self.config_path = config_path
        self.component_health = {}
        self.recovery_plans = {}
        self.checkpoints = {}
        self.recovery_history = []
        self.monitoring_active = False
        self.monitoring_thread = None
        self.lock = threading.Lock()
        
        # Recovery statistics
        self.recovery_stats = {
            'total_failures': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'average_recovery_time': 0.0,
            'uptime_percentage': 100.0,
            'last_failure': None
        }
        
        # Load configurations
        self._load_recovery_configurations()
        self._initialize_component_monitoring()
        
        logger.info("🛡️ System Recovery Manager initialized")
        logger.info(f"   📊 Components monitored: {len(self.component_health)}")
        logger.info(f"   🔧 Recovery plans loaded: {len(self.recovery_plans)}")

    def _load_recovery_configurations(self):
        """Load recovery configurations"""
        # Default recovery plans for system components
        self.recovery_plans = {
            'advanced_technical_analysis_engine': RecoveryPlan(
                component_name='advanced_technical_analysis_engine',
                actions=[RecoveryAction.RESET, RecoveryAction.RESTART],
                timeout=300,
                max_attempts=3,
                dependencies=['shared_database_utils']
            ),
            'ollama_subprocess_manager': RecoveryPlan(
                component_name='ollama_subprocess_manager',
                actions=[RecoveryAction.RESTART, RecoveryAction.RESET],
                timeout=180,
                max_attempts=5,
                dependencies=[]
            ),
            'shared_database_utils': RecoveryPlan(
                component_name='shared_database_utils',
                actions=[RecoveryAction.RESET, RecoveryAction.ROLLBACK],
                timeout=120,
                max_attempts=3,
                dependencies=[]
            ),
            'ensemble_voting_system': RecoveryPlan(
                component_name='ensemble_voting_system',
                actions=[RecoveryAction.RESTART, RecoveryAction.FAILOVER],
                timeout=240,
                max_attempts=3,
                dependencies=['ollama_subprocess_manager']
            ),
            'enhanced_ai_trading_integration': RecoveryPlan(
                component_name='enhanced_ai_trading_integration',
                actions=[RecoveryAction.RESTART, RecoveryAction.RESET],
                timeout=300,
                max_attempts=2,
                dependencies=['advanced_technical_analysis_engine', 'ollama_subprocess_manager']
            )
        }

    def _initialize_component_monitoring(self):
        """Initialize component health monitoring"""
        for component_name in self.recovery_plans.keys():
            self.component_health[component_name] = ComponentHealth(
                component_name=component_name,
                status=ComponentStatus.UNKNOWN,
                last_check=datetime.now(),
                error_count=0,
                recovery_attempts=0,
                performance_metrics={}
            )

    def start_monitoring(self):
        """Start continuous system monitoring"""
        if self.monitoring_active:
            logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("🔍 System monitoring started")

    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10)
        
        logger.info("🛑 System monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                self._check_all_components()
                self._update_uptime_statistics()
                time.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                time.sleep(60)  # Wait longer on error

    def _check_all_components(self):
        """Check health of all monitored components"""
        for component_name in self.component_health.keys():
            try:
                self._check_component_health(component_name)
            except Exception as e:
                logger.error(f"Error checking {component_name}: {e}")
                self._mark_component_failed(component_name, str(e))

    def _check_component_health(self, component_name: str):
        """Check health of a specific component"""
        with self.lock:
            health = self.component_health[component_name]
            
            # Component-specific health checks
            if component_name == 'advanced_technical_analysis_engine':
                status = self._check_ta_engine_health()
            elif component_name == 'ollama_subprocess_manager':
                status = self._check_ollama_manager_health()
            elif component_name == 'shared_database_utils':
                status = self._check_database_health()
            elif component_name == 'ensemble_voting_system':
                status = self._check_ensemble_health()
            elif component_name == 'enhanced_ai_trading_integration':
                status = self._check_integration_health()
            else:
                status = ComponentStatus.UNKNOWN
            
            # Update health status
            previous_status = health.status
            health.status = status
            health.last_check = datetime.now()
            
            # Trigger recovery if component failed
            if status == ComponentStatus.FAILED and previous_status != ComponentStatus.FAILED:
                logger.warning(f"🚨 Component failure detected: {component_name}")
                self._trigger_recovery(component_name)

    def _check_ta_engine_health(self) -> ComponentStatus:
        """Check Technical Analysis Engine health"""
        try:
            # Import and test TA engine
            from advanced_technical_analysis_engine import AdvancedTechnicalAnalysisEngine
            
            # Quick health check
            ta_engine = AdvancedTechnicalAnalysisEngine()
            test_data = ta_engine._get_mock_market_data('HEALTH_CHECK')
            
            # Test a simple calculation
            start_time = time.time()
            results = ta_engine.calculate_advanced_momentum_indicators(
                test_data['highs'][:20], test_data['lows'][:20], 
                test_data['closes'][:20], test_data['volumes'][:20]
            )
            calculation_time = time.time() - start_time
            
            # Check if results are valid and performance is acceptable
            if results and calculation_time < 10.0:  # Should complete in under 10 seconds
                return ComponentStatus.HEALTHY
            else:
                return ComponentStatus.DEGRADED
                
        except Exception as e:
            logger.error(f"TA Engine health check failed: {e}")
            return ComponentStatus.FAILED

    def _check_ollama_manager_health(self) -> ComponentStatus:
        """Check Ollama Manager health"""
        try:
            from ollama_subprocess_manager import ollama_manager
            
            # Test manager status
            status = ollama_manager.get_all_model_status()
            
            if status and status.get('manager_status') == 'operational':
                return ComponentStatus.HEALTHY
            else:
                return ComponentStatus.DEGRADED
                
        except Exception as e:
            logger.error(f"Ollama Manager health check failed: {e}")
            return ComponentStatus.FAILED

    def _check_database_health(self) -> ComponentStatus:
        """Check Database health"""
        try:
            from shared_database_utils import db_manager
            
            # Test database connection
            with db_manager.get_connection('advanced_technical_analysis') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                if result and result[0] == 1:
                    return ComponentStatus.HEALTHY
                else:
                    return ComponentStatus.DEGRADED
                    
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return ComponentStatus.FAILED

    def _check_ensemble_health(self) -> ComponentStatus:
        """Check Ensemble Voting System health"""
        try:
            # Check if ensemble system is responsive
            # This would depend on your specific ensemble implementation
            return ComponentStatus.HEALTHY
            
        except Exception as e:
            logger.error(f"Ensemble health check failed: {e}")
            return ComponentStatus.FAILED

    def _check_integration_health(self) -> ComponentStatus:
        """Check Enhanced AI Trading Integration health"""
        try:
            from enhanced_ai_trading_integration import EnhancedAITradingIntegration
            
            # Test integration system
            integration = EnhancedAITradingIntegration()
            status = integration.get_system_status()
            
            if status and status.get('status') == 'OPERATIONAL':
                return ComponentStatus.HEALTHY
            else:
                return ComponentStatus.DEGRADED
                
        except Exception as e:
            logger.error(f"Integration health check failed: {e}")
            return ComponentStatus.FAILED

    def _mark_component_failed(self, component_name: str, error_message: str):
        """Mark a component as failed"""
        with self.lock:
            health = self.component_health[component_name]
            health.status = ComponentStatus.FAILED
            health.error_count += 1
            health.last_error = error_message
            health.last_check = datetime.now()
            
            # Update statistics
            self.recovery_stats['total_failures'] += 1
            self.recovery_stats['last_failure'] = datetime.now()

    def _trigger_recovery(self, component_name: str):
        """Trigger recovery for a failed component"""
        logger.info(f"🔧 Triggering recovery for {component_name}")
        
        recovery_plan = self.recovery_plans.get(component_name)
        if not recovery_plan:
            logger.error(f"No recovery plan found for {component_name}")
            return False
        
        # Create checkpoint before recovery
        checkpoint_id = self.create_checkpoint(f"pre_recovery_{component_name}")
        
        # Execute recovery actions
        recovery_success = self._execute_recovery_plan(component_name, recovery_plan)
        
        # Update statistics
        with self.lock:
            health = self.component_health[component_name]
            health.recovery_attempts += 1
            
            if recovery_success:
                self.recovery_stats['successful_recoveries'] += 1
                health.status = ComponentStatus.HEALTHY
                health.error_count = 0
                logger.info(f"✅ Recovery successful for {component_name}")
            else:
                self.recovery_stats['failed_recoveries'] += 1
                logger.error(f"❌ Recovery failed for {component_name}")
        
        # Record recovery attempt
        self.recovery_history.append({
            'timestamp': datetime.now(),
            'component': component_name,
            'success': recovery_success,
            'checkpoint_id': checkpoint_id,
            'actions_attempted': recovery_plan.actions
        })
        
        return recovery_success

    def _execute_recovery_plan(self, component_name: str, plan: RecoveryPlan) -> bool:
        """Execute recovery plan for a component"""
        logger.info(f"Executing recovery plan for {component_name}")
        
        for action in plan.actions:
            try:
                logger.info(f"Attempting {action.value} for {component_name}")
                
                if action == RecoveryAction.RESTART:
                    success = self._restart_component(component_name)
                elif action == RecoveryAction.RESET:
                    success = self._reset_component(component_name)
                elif action == RecoveryAction.FAILOVER:
                    success = self._failover_component(component_name)
                elif action == RecoveryAction.ROLLBACK:
                    success = self._rollback_component(component_name)
                else:
                    logger.warning(f"Unknown recovery action: {action}")
                    continue
                
                if success:
                    # Wait and verify recovery
                    time.sleep(10)
                    if self._verify_component_recovery(component_name):
                        logger.info(f"✅ {action.value} successful for {component_name}")
                        return True
                    else:
                        logger.warning(f"⚠️ {action.value} completed but component still unhealthy")
                
            except Exception as e:
                logger.error(f"Recovery action {action.value} failed for {component_name}: {e}")
                continue
        
        logger.error(f"All recovery actions failed for {component_name}")
        return False

    def _restart_component(self, component_name: str) -> bool:
        """Restart a component"""
        try:
            # Component-specific restart logic
            if component_name == 'ollama_subprocess_manager':
                # Clear cache and reinitialize
                from ollama_subprocess_manager import ollama_manager
                ollama_manager.clear_cache()
                return True
            elif component_name == 'advanced_technical_analysis_engine':
                # Reinitialize the engine
                return True
            else:
                logger.warning(f"No restart procedure defined for {component_name}")
                return False
                
        except Exception as e:
            logger.error(f"Restart failed for {component_name}: {e}")
            return False

    def _reset_component(self, component_name: str) -> bool:
        """Reset a component to default state"""
        try:
            # Component-specific reset logic
            logger.info(f"Resetting {component_name}")
            return True
            
        except Exception as e:
            logger.error(f"Reset failed for {component_name}: {e}")
            return False

    def _failover_component(self, component_name: str) -> bool:
        """Failover to backup component"""
        try:
            # Implement failover logic
            logger.info(f"Failing over {component_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failover failed for {component_name}: {e}")
            return False

    def _rollback_component(self, component_name: str) -> bool:
        """Rollback component to previous working state"""
        try:
            # Find latest checkpoint
            latest_checkpoint = self._get_latest_checkpoint()
            if latest_checkpoint:
                return self._restore_from_checkpoint(latest_checkpoint.checkpoint_id)
            else:
                logger.warning("No checkpoint available for rollback")
                return False
                
        except Exception as e:
            logger.error(f"Rollback failed for {component_name}: {e}")
            return False

    def _verify_component_recovery(self, component_name: str) -> bool:
        """Verify that component has recovered"""
        try:
            self._check_component_health(component_name)
            health = self.component_health[component_name]
            return health.status in [ComponentStatus.HEALTHY, ComponentStatus.DEGRADED]
            
        except Exception as e:
            logger.error(f"Recovery verification failed for {component_name}: {e}")
            return False

    def create_checkpoint(self, description: str = None) -> str:
        """Create a system checkpoint"""
        checkpoint_id = f"checkpoint_{int(time.time())}"
        
        try:
            # Collect system state
            component_states = {}
            for name, health in self.component_health.items():
                component_states[name] = asdict(health)
            
            # Create checkpoint
            checkpoint = SystemCheckpoint(
                timestamp=datetime.now(),
                component_states=component_states,
                configuration_snapshot={},  # Would include config snapshots
                performance_baseline={},    # Would include performance metrics
                checkpoint_id=checkpoint_id
            )
            
            # Store checkpoint
            self.checkpoints[checkpoint_id] = checkpoint
            
            # Save to disk
            checkpoint_file = f"checkpoints/{checkpoint_id}.pkl"
            os.makedirs("checkpoints", exist_ok=True)
            
            with open(checkpoint_file, 'wb') as f:
                pickle.dump(checkpoint, f)
            
            logger.info(f"📸 Checkpoint created: {checkpoint_id}")
            return checkpoint_id
            
        except Exception as e:
            logger.error(f"Checkpoint creation failed: {e}")
            return None

    def _get_latest_checkpoint(self) -> Optional[SystemCheckpoint]:
        """Get the latest system checkpoint"""
        if not self.checkpoints:
            return None
        
        latest_checkpoint = max(self.checkpoints.values(), key=lambda c: c.timestamp)
        return latest_checkpoint

    def _restore_from_checkpoint(self, checkpoint_id: str) -> bool:
        """Restore system from checkpoint"""
        try:
            checkpoint = self.checkpoints.get(checkpoint_id)
            if not checkpoint:
                # Try loading from disk
                checkpoint_file = f"checkpoints/{checkpoint_id}.pkl"
                if os.path.exists(checkpoint_file):
                    with open(checkpoint_file, 'rb') as f:
                        checkpoint = pickle.load(f)
                else:
                    logger.error(f"Checkpoint {checkpoint_id} not found")
                    return False
            
            # Restore component states
            for name, state_data in checkpoint.component_states.items():
                if name in self.component_health:
                    # Restore health data (excluding dynamic fields)
                    health = self.component_health[name]
                    health.error_count = state_data.get('error_count', 0)
                    health.recovery_attempts = state_data.get('recovery_attempts', 0)
            
            logger.info(f"🔄 System restored from checkpoint: {checkpoint_id}")
            return True
            
        except Exception as e:
            logger.error(f"Checkpoint restoration failed: {e}")
            return False

    def _update_uptime_statistics(self):
        """Update system uptime statistics"""
        try:
            # Calculate uptime based on component health
            healthy_components = sum(1 for health in self.component_health.values() 
                                   if health.status == ComponentStatus.HEALTHY)
            total_components = len(self.component_health)
            
            if total_components > 0:
                current_uptime = (healthy_components / total_components) * 100
                
                # Update rolling average
                self.recovery_stats['uptime_percentage'] = (
                    self.recovery_stats['uptime_percentage'] * 0.95 + current_uptime * 0.05
                )
            
        except Exception as e:
            logger.error(f"Uptime statistics update failed: {e}")

    def get_system_health_report(self) -> Dict[str, Any]:
        """Get comprehensive system health report"""
        with self.lock:
            healthy_count = sum(1 for h in self.component_health.values() 
                              if h.status == ComponentStatus.HEALTHY)
            total_count = len(self.component_health)
            
            return {
                'timestamp': datetime.now(),
                'overall_health': 'HEALTHY' if healthy_count == total_count else 'DEGRADED',
                'uptime_percentage': self.recovery_stats['uptime_percentage'],
                'component_health': {name: asdict(health) for name, health in self.component_health.items()},
                'recovery_statistics': self.recovery_stats,
                'recent_recoveries': self.recovery_history[-10:],  # Last 10 recoveries
                'monitoring_active': self.monitoring_active
            }

# Global recovery manager instance
recovery_manager = SystemRecoveryManager()

# Convenience functions
def start_system_monitoring():
    """Start system monitoring (convenience function)"""
    recovery_manager.start_monitoring()

def create_system_checkpoint(description: str = None) -> str:
    """Create system checkpoint (convenience function)"""
    return recovery_manager.create_checkpoint(description)

def get_system_health() -> Dict[str, Any]:
    """Get system health report (convenience function)"""
    return recovery_manager.get_system_health_report()
