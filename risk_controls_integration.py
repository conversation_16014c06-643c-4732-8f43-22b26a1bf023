#!/usr/bin/env python3
"""
RISK CONTROLS INTEGRATION SYSTEM
Enhanced risk management with AI model-specific controls, position sizing based on confidence,
and emergency stop mechanisms. Integrates with existing WorkingRiskManager.
"""

import sqlite3
import time
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import math

# Import existing risk management
try:
    from working_risk_management import WorkingRiskManager
    from model_output_validation import ModelOutputValidator, ValidationResult
    from enhanced_error_handling import EnhancedErrorHandler
except ImportError as e:
    logging.warning(f"Some dependencies not available: {e}")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class EmergencyAction(Enum):
    REDUCE_POSITIONS = "reduce_positions"
    HALT_TRADING = "halt_trading"
    LIQUIDATE_ALL = "liquidate_all"
    CONSERVATIVE_MODE = "conservative_mode"

@dataclass
class RiskAssessment:
    """AI model risk assessment"""
    model_name: str
    symbol: str
    confidence_level: float
    risk_score: float
    position_size_multiplier: float
    max_exposure_limit: float
    stop_loss_adjustment: float
    risk_level: RiskLevel
    risk_factors: List[str]
    timestamp: datetime

@dataclass
class EmergencyStop:
    """Emergency stop event"""
    trigger_id: str
    trigger_reason: str
    failed_models: List[str]
    action_taken: EmergencyAction
    positions_affected: int
    total_exposure_reduced: float
    timestamp: datetime
    resolved: bool = False

class RiskControlsIntegration:
    """
    Enhanced risk management system with AI model-specific controls
    Integrates with existing risk management while adding AI-aware features
    """
    
    def __init__(self, initial_capital: float = 100000.0, db_path: str = "risk_controls.db"):
        self.initial_capital = initial_capital
        self.db_path = db_path
        self.setup_database()
        
        # Initialize existing risk manager
        try:
            self.base_risk_manager = WorkingRiskManager(initial_capital)
            self.validator = ModelOutputValidator()
            self.error_handler = EnhancedErrorHandler()
        except Exception as e:
            logger.warning(f"Some components not available: {e}")
            self.base_risk_manager = None
            self.validator = None
            self.error_handler = None
        
        # Risk control configuration
        self.risk_config = {
            'confidence_thresholds': {
                'high_confidence': 0.8,
                'medium_confidence': 0.6,
                'low_confidence': 0.4,
                'minimum_confidence': 0.3
            },
            'position_sizing': {
                'high_confidence_multiplier': 1.0,
                'medium_confidence_multiplier': 0.7,
                'low_confidence_multiplier': 0.4,
                'minimum_confidence_multiplier': 0.2
            },
            'exposure_limits': {
                'max_single_position': 0.1,  # 10% of portfolio
                'max_total_exposure': 0.8,   # 80% of portfolio
                'max_ai_exposure': 0.6,      # 60% for AI-driven trades
                'emergency_threshold': 0.9   # 90% triggers emergency
            },
            'model_reliability': {
                'minimum_reliability': 0.5,
                'degraded_performance_threshold': 0.7,
                'emergency_failure_threshold': 0.3
            }
        }
        
        # Emergency controls
        self.emergency_status = {
            'active': False,
            'level': None,
            'triggered_at': None,
            'failed_models': [],
            'actions_taken': []
        }
        
        # Performance tracking
        self.risk_metrics = {
            'total_risk_assessments': 0,
            'high_risk_blocks': 0,
            'position_size_reductions': 0,
            'emergency_stops': 0,
            'avg_risk_score': 0.0
        }
        
        logger.info("🛡️ Risk Controls Integration System initialized")
        logger.info(f"   💰 Initial capital: ${initial_capital:,.2f}")
        logger.info(f"   📊 Risk thresholds configured")
        logger.info(f"   🚨 Emergency controls active")
        logger.info(f"   🗃️ Database: {self.db_path}")
    
    def setup_database(self):
        """Setup risk controls database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS risk_assessments (
                id INTEGER PRIMARY KEY,
                model_name TEXT,
                symbol TEXT,
                confidence_level REAL,
                risk_score REAL,
                position_size_multiplier REAL,
                max_exposure_limit REAL,
                stop_loss_adjustment REAL,
                risk_level TEXT,
                risk_factors TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS emergency_stops (
                id INTEGER PRIMARY KEY,
                trigger_id TEXT,
                trigger_reason TEXT,
                failed_models TEXT,
                action_taken TEXT,
                positions_affected INTEGER,
                total_exposure_reduced REAL,
                timestamp DATETIME,
                resolved BOOLEAN
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS position_adjustments (
                id INTEGER PRIMARY KEY,
                symbol TEXT,
                original_size REAL,
                adjusted_size REAL,
                adjustment_reason TEXT,
                confidence_factor REAL,
                risk_factor REAL,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_risk_scores (
                id INTEGER PRIMARY KEY,
                model_name TEXT,
                reliability_score REAL,
                avg_confidence REAL,
                risk_contribution REAL,
                failure_rate REAL,
                last_failure DATETIME,
                risk_rating TEXT,
                last_updated DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("✅ Risk controls database initialized")
    
    def assess_trading_risk(self, model_output: Dict[str, Any], model_name: str, 
                          symbol: str) -> RiskAssessment:
        """
        Comprehensive risk assessment for AI model trading signals
        Returns risk assessment with position sizing and exposure recommendations
        """
        start_time = time.time()
        
        confidence = model_output.get('confidence', 0.0)
        action = model_output.get('action', 'HOLD')
        
        # Calculate base risk score
        risk_score = self._calculate_risk_score(model_output, model_name, symbol)
        
        # Determine risk level
        risk_level = self._determine_risk_level(risk_score, confidence)
        
        # Calculate position size multiplier based on confidence
        position_multiplier = self._calculate_position_multiplier(confidence, risk_score)
        
        # Calculate exposure limits
        max_exposure = self._calculate_max_exposure(confidence, risk_level)
        
        # Calculate stop loss adjustment
        stop_loss_adjustment = self._calculate_stop_loss_adjustment(confidence, risk_score)
        
        # Identify risk factors
        risk_factors = self._identify_risk_factors(model_output, model_name, confidence)
        
        # Create risk assessment
        assessment = RiskAssessment(
            model_name=model_name,
            symbol=symbol,
            confidence_level=confidence,
            risk_score=risk_score,
            position_size_multiplier=position_multiplier,
            max_exposure_limit=max_exposure,
            stop_loss_adjustment=stop_loss_adjustment,
            risk_level=risk_level,
            risk_factors=risk_factors,
            timestamp=datetime.now()
        )
        
        # Log assessment
        self._log_risk_assessment(assessment)
        
        # Update metrics
        self.risk_metrics['total_risk_assessments'] += 1
        self.risk_metrics['avg_risk_score'] = (
            (self.risk_metrics['avg_risk_score'] * (self.risk_metrics['total_risk_assessments'] - 1) + risk_score) /
            self.risk_metrics['total_risk_assessments']
        )
        
        logger.info(f"🛡️ Risk assessment for {symbol}: {risk_level.value} risk, {position_multiplier:.2f}x position")
        
        return assessment
    
    def apply_risk_controls(self, trading_signal: Dict[str, Any], risk_assessment: RiskAssessment) -> Dict[str, Any]:
        """
        Apply risk controls to trading signal
        Returns modified trading signal with risk-adjusted parameters
        """
        
        controlled_signal = trading_signal.copy()
        
        # Apply position size controls
        if 'position_size' in controlled_signal:
            original_size = controlled_signal['position_size']
            adjusted_size = original_size * risk_assessment.position_size_multiplier
            
            # Ensure within exposure limits
            adjusted_size = min(adjusted_size, risk_assessment.max_exposure_limit)
            
            controlled_signal['position_size'] = adjusted_size
            
            # Log position adjustment if significant change
            if abs(adjusted_size - original_size) > 0.01:
                self._log_position_adjustment(
                    risk_assessment.symbol, original_size, adjusted_size,
                    f"Risk control: {risk_assessment.risk_level.value} risk",
                    risk_assessment.confidence_level, risk_assessment.risk_score
                )
        
        # Apply stop loss adjustments
        if 'stop_loss' in controlled_signal and controlled_signal['stop_loss']:
            original_stop = controlled_signal['stop_loss']
            # Tighten stop loss for higher risk
            adjustment_factor = 1.0 - (risk_assessment.stop_loss_adjustment * 0.1)
            controlled_signal['stop_loss'] = original_stop * adjustment_factor
        
        # Add risk metadata
        controlled_signal['risk_assessment'] = {
            'risk_level': risk_assessment.risk_level.value,
            'risk_score': risk_assessment.risk_score,
            'confidence_level': risk_assessment.confidence_level,
            'position_multiplier': risk_assessment.position_size_multiplier,
            'risk_factors': risk_assessment.risk_factors
        }
        
        # Check for high-risk blocks
        if risk_assessment.risk_level == RiskLevel.CRITICAL:
            controlled_signal['action'] = 'HOLD'
            controlled_signal['risk_blocked'] = True
            self.risk_metrics['high_risk_blocks'] += 1
            logger.warning(f"🚨 CRITICAL RISK: Blocked {trading_signal.get('action')} for {risk_assessment.symbol}")
        
        return controlled_signal
    
    def check_emergency_conditions(self, failed_models: List[str], system_health: Dict[str, Any]) -> Optional[EmergencyStop]:
        """
        Check for emergency conditions requiring immediate action
        Returns emergency stop if conditions are met
        """
        
        # Check if already in emergency mode
        if self.emergency_status['active']:
            return None
        
        emergency_triggered = False
        trigger_reason = ""
        action_required = EmergencyAction.CONSERVATIVE_MODE
        
        # Check for multiple model failures
        if len(failed_models) >= 3:
            emergency_triggered = True
            trigger_reason = f"Multiple model failures: {len(failed_models)} models"
            action_required = EmergencyAction.HALT_TRADING
        
        # Check for system performance degradation
        performance_degradation = system_health.get('degradation_level', 1.0)
        if performance_degradation > 2.0:  # 100% degradation
            emergency_triggered = True
            trigger_reason = f"Critical performance degradation: {performance_degradation:.2f}x"
            action_required = EmergencyAction.REDUCE_POSITIONS
        
        # Check for high error rates
        error_rate = system_health.get('error_rate', 0.0)
        if error_rate > 0.5:  # 50% error rate
            emergency_triggered = True
            trigger_reason = f"High error rate: {error_rate:.2%}"
            action_required = EmergencyAction.CONSERVATIVE_MODE
        
        # Check for exposure limits
        current_exposure = self._calculate_current_exposure()
        if current_exposure > self.risk_config['exposure_limits']['emergency_threshold']:
            emergency_triggered = True
            trigger_reason = f"Excessive exposure: {current_exposure:.2%}"
            action_required = EmergencyAction.REDUCE_POSITIONS
        
        if emergency_triggered:
            emergency_stop = EmergencyStop(
                trigger_id=f"EMRG_{int(time.time())}",
                trigger_reason=trigger_reason,
                failed_models=failed_models,
                action_taken=action_required,
                positions_affected=0,  # Will be updated when action is taken
                total_exposure_reduced=0.0,  # Will be updated when action is taken
                timestamp=datetime.now()
            )
            
            # Activate emergency mode
            self.emergency_status['active'] = True
            self.emergency_status['level'] = action_required
            self.emergency_status['triggered_at'] = datetime.now()
            self.emergency_status['failed_models'] = failed_models
            
            # Log emergency stop
            self._log_emergency_stop(emergency_stop)
            
            # Update metrics
            self.risk_metrics['emergency_stops'] += 1
            
            logger.critical(f"🚨 EMERGENCY STOP TRIGGERED: {trigger_reason}")
            logger.critical(f"🚨 Action required: {action_required.value}")
            
            return emergency_stop
        
        return None

    def _calculate_risk_score(self, model_output: Dict[str, Any], model_name: str, symbol: str) -> float:
        """Calculate comprehensive risk score"""

        confidence = model_output.get('confidence', 0.0)
        action = model_output.get('action', 'HOLD')

        # Base risk from confidence (inverse relationship)
        confidence_risk = 1.0 - confidence

        # Action risk (HOLD is safest)
        action_risk = {'HOLD': 0.0, 'BUY': 0.3, 'SELL': 0.3}.get(action, 0.5)

        # Model reliability risk
        model_reliability = self._get_model_reliability(model_name)
        reliability_risk = 1.0 - model_reliability

        # Market volatility risk (simplified)
        volatility_risk = 0.2  # Default moderate volatility

        # Combine risk factors
        total_risk = (
            confidence_risk * 0.4 +
            action_risk * 0.2 +
            reliability_risk * 0.3 +
            volatility_risk * 0.1
        )

        return min(1.0, max(0.0, total_risk))

    def _determine_risk_level(self, risk_score: float, confidence: float) -> RiskLevel:
        """Determine risk level based on score and confidence"""

        if risk_score > 0.8 or confidence < 0.3:
            return RiskLevel.CRITICAL
        elif risk_score > 0.6 or confidence < 0.5:
            return RiskLevel.HIGH
        elif risk_score > 0.4 or confidence < 0.7:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _calculate_position_multiplier(self, confidence: float, risk_score: float) -> float:
        """Calculate position size multiplier based on confidence and risk"""

        config = self.risk_config['position_sizing']
        thresholds = self.risk_config['confidence_thresholds']

        # Base multiplier from confidence
        if confidence >= thresholds['high_confidence']:
            base_multiplier = config['high_confidence_multiplier']
        elif confidence >= thresholds['medium_confidence']:
            base_multiplier = config['medium_confidence_multiplier']
        elif confidence >= thresholds['low_confidence']:
            base_multiplier = config['low_confidence_multiplier']
        else:
            base_multiplier = config['minimum_confidence_multiplier']

        # Adjust for risk score
        risk_adjustment = 1.0 - (risk_score * 0.5)  # Reduce by up to 50% for high risk

        final_multiplier = base_multiplier * risk_adjustment

        return max(0.1, min(1.0, final_multiplier))  # Clamp between 10% and 100%

    def _calculate_max_exposure(self, confidence: float, risk_level: RiskLevel) -> float:
        """Calculate maximum exposure limit"""

        base_limits = self.risk_config['exposure_limits']

        if risk_level == RiskLevel.CRITICAL:
            return base_limits['max_single_position'] * 0.5  # 50% of normal
        elif risk_level == RiskLevel.HIGH:
            return base_limits['max_single_position'] * 0.7  # 70% of normal
        elif risk_level == RiskLevel.MEDIUM:
            return base_limits['max_single_position'] * 0.9  # 90% of normal
        else:
            return base_limits['max_single_position']  # Full exposure allowed

    def _calculate_stop_loss_adjustment(self, confidence: float, risk_score: float) -> float:
        """Calculate stop loss tightening factor"""

        # Tighten stop loss for lower confidence and higher risk
        confidence_factor = 1.0 - confidence  # 0 to 1
        risk_factor = risk_score  # 0 to 1

        # Adjustment ranges from 0 (no tightening) to 0.5 (50% tighter)
        adjustment = (confidence_factor + risk_factor) / 4.0

        return min(0.5, adjustment)

    def _identify_risk_factors(self, model_output: Dict[str, Any], model_name: str, confidence: float) -> List[str]:
        """Identify specific risk factors"""

        risk_factors = []

        if confidence < 0.5:
            risk_factors.append("Low model confidence")

        if confidence < 0.3:
            risk_factors.append("Very low model confidence")

        model_reliability = self._get_model_reliability(model_name)
        if model_reliability < 0.7:
            risk_factors.append("Low model reliability")

        if not model_output.get('reasoning'):
            risk_factors.append("No reasoning provided")

        if model_output.get('action') != 'HOLD' and not model_output.get('price_target'):
            risk_factors.append("No price target for active trade")

        if len(model_output.get('reasoning', '')) < 50:
            risk_factors.append("Insufficient reasoning detail")

        return risk_factors

    def _get_model_reliability(self, model_name: str) -> float:
        """Get model reliability score from database"""

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT reliability_score FROM model_risk_scores
                WHERE model_name = ? ORDER BY last_updated DESC LIMIT 1
            ''', (model_name,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return result[0]
            else:
                # Default reliability for new models
                return 0.8

        except Exception as e:
            logger.warning(f"Could not get model reliability for {model_name}: {e}")
            return 0.8  # Default

    def _calculate_current_exposure(self) -> float:
        """Calculate current portfolio exposure"""

        # This would integrate with your existing portfolio management
        # For now, return a placeholder
        return 0.5  # 50% exposure

    def _log_emergency_stop(self, emergency_stop: EmergencyStop):
        """Log emergency stop event"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO emergency_stops
            (trigger_id, trigger_reason, failed_models, action_taken,
             positions_affected, total_exposure_reduced, timestamp, resolved)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            emergency_stop.trigger_id, emergency_stop.trigger_reason,
            json.dumps(emergency_stop.failed_models), emergency_stop.action_taken.value,
            emergency_stop.positions_affected, emergency_stop.total_exposure_reduced,
            emergency_stop.timestamp, emergency_stop.resolved
        ))

        conn.commit()
        conn.close()

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk management summary"""

        return {
            'risk_metrics': self.risk_metrics.copy(),
            'emergency_status': self.emergency_status.copy(),
            'risk_config': self.risk_config.copy(),
            'current_exposure': self._calculate_current_exposure(),
            'database_path': self.db_path
        }
