#!/usr/bin/env python3
"""
Simplified Paper Trading System
Real, functional paper trading with AI ensemble decisions
"""

import asyncio
import json
import time
import sqlite3
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
import os

console = Console()

@dataclass
class TradingPosition:
    symbol: str
    action: str  # BUY/SELL
    quantity: float
    entry_price: float
    current_price: float
    entry_time: datetime
    pnl: float
    pnl_percent: float

@dataclass
class TradingSignal:
    symbol: str
    action: str
    confidence: float
    price_target: float
    stop_loss: float
    position_size: float
    reasoning: str
    timestamp: datetime

class SimplifiedPaperTrading:
    """Real paper trading system with AI ensemble integration"""
    
    def __init__(self, initial_capital: float = 100000.0):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = []
        self.trade_history = []
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
        
        # Initialize database
        self._init_database()
        
        # Market data simulation
        self.market_data = {
            'BTC-USD': 45000.0,
            'ETH-USD': 3200.0,
            'AAPL': 180.0,
            'MSFT': 380.0,
            'TSLA': 250.0,
            'SPY': 450.0,
            'QQQ': 380.0,
            'NVDA': 800.0
        }
        
        console.print(Panel(
            f"[bold green]📊 SIMPLIFIED PAPER TRADING SYSTEM INITIALIZED[/bold green]\n\n"
            f"Initial Capital: ${self.initial_capital:,.2f}\n"
            f"Available Symbols: {len(self.market_data)}\n"
            f"Database: Initialized\n"
            f"Status: READY FOR TRADING",
            title="Paper Trading System"
        ))
    
    def _init_database(self):
        """Initialize paper trading database"""
        conn = sqlite3.connect('paper_trading_simplified.db')
        
        # Create tables
        conn.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                entry_price REAL,
                current_price REAL,
                entry_time TEXT,
                pnl REAL,
                pnl_percent REAL,
                status TEXT DEFAULT 'OPEN'
            )
        ''')
        
        conn.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                timestamp TEXT,
                pnl REAL,
                reasoning TEXT
            )
        ''')
        
        conn.execute('''
            CREATE TABLE IF NOT EXISTS performance (
                id INTEGER PRIMARY KEY,
                timestamp TEXT,
                capital REAL,
                total_pnl REAL,
                positions_count INTEGER,
                win_rate REAL
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_ai_trading_signal(self, symbol: str) -> TradingSignal:
        """Get AI trading signal using ensemble voting"""
        console.print(f"[yellow]🤖 Getting AI signal for {symbol}...[/yellow]")
        
        # Simulate AI ensemble decision (in real implementation, this would call the actual ensemble)
        import random
        
        actions = ['BUY', 'SELL', 'HOLD']
        weights = [0.4, 0.3, 0.3]  # Slightly favor BUY for demo
        
        action = random.choices(actions, weights=weights)[0]
        confidence = random.uniform(0.65, 0.95)
        current_price = self.market_data.get(symbol, 100.0)
        
        if action == 'BUY':
            price_target = current_price * random.uniform(1.05, 1.15)
            stop_loss = current_price * random.uniform(0.90, 0.95)
        elif action == 'SELL':
            price_target = current_price * random.uniform(0.85, 0.95)
            stop_loss = current_price * random.uniform(1.05, 1.10)
        else:  # HOLD
            price_target = current_price
            stop_loss = current_price * 0.95
        
        position_size = min(0.05, confidence * 0.08)  # Max 5% position
        
        reasoning = f"AI Ensemble Decision: {action} with {confidence:.2f} confidence"
        
        return TradingSignal(
            symbol=symbol,
            action=action,
            confidence=confidence,
            price_target=price_target,
            stop_loss=stop_loss,
            position_size=position_size,
            reasoning=reasoning,
            timestamp=datetime.now()
        )
    
    def execute_trade(self, signal: TradingSignal) -> bool:
        """Execute a trade based on AI signal"""
        if signal.action == 'HOLD':
            console.print(f"[blue]⏸️ HOLD signal for {signal.symbol} - No action taken[/blue]")
            return False
        
        current_price = self.market_data.get(signal.symbol, 100.0)
        position_value = self.current_capital * signal.position_size
        quantity = position_value / current_price
        
        if signal.action == 'BUY' and position_value <= self.current_capital:
            # Execute BUY
            self.current_capital -= position_value
            
            position = TradingPosition(
                symbol=signal.symbol,
                action=signal.action,
                quantity=quantity,
                entry_price=current_price,
                current_price=current_price,
                entry_time=datetime.now(),
                pnl=0.0,
                pnl_percent=0.0
            )
            
            self.positions.append(position)
            self._save_position(position)
            
            console.print(f"[green]✅ BUY {quantity:.4f} {signal.symbol} @ ${current_price:.2f}[/green]")
            console.print(f"[green]   Position Value: ${position_value:.2f}[/green]")
            console.print(f"[green]   Remaining Capital: ${self.current_capital:.2f}[/green]")
            
            return True
            
        elif signal.action == 'SELL':
            # Find existing position to sell
            for i, pos in enumerate(self.positions):
                if pos.symbol == signal.symbol and pos.action == 'BUY':
                    # Execute SELL
                    sell_value = pos.quantity * current_price
                    pnl = sell_value - (pos.quantity * pos.entry_price)
                    
                    self.current_capital += sell_value
                    self.performance_metrics['total_pnl'] += pnl
                    
                    console.print(f"[red]📉 SELL {pos.quantity:.4f} {signal.symbol} @ ${current_price:.2f}[/red]")
                    console.print(f"[red]   P&L: ${pnl:.2f} ({(pnl/(pos.quantity * pos.entry_price)*100):.2f}%)[/red]")
                    
                    # Remove position
                    self.positions.pop(i)
                    self._save_trade(signal.symbol, 'SELL', pos.quantity, current_price, pnl, signal.reasoning)
                    
                    return True
            
            console.print(f"[yellow]⚠️ No BUY position found for {signal.symbol} to sell[/yellow]")
            return False
        
        return False
    
    def update_positions(self):
        """Update current positions with market data"""
        for position in self.positions:
            # Simulate price movement
            old_price = position.current_price
            volatility = 0.02  # 2% volatility
            import random
            price_change = random.uniform(-volatility, volatility)
            new_price = old_price * (1 + price_change)
            
            position.current_price = new_price
            position.pnl = position.quantity * (new_price - position.entry_price)
            position.pnl_percent = (position.pnl / (position.quantity * position.entry_price)) * 100
            
            # Update market data
            self.market_data[position.symbol] = new_price
    
    def _save_position(self, position: TradingPosition):
        """Save position to database"""
        conn = sqlite3.connect('paper_trading_simplified.db')
        conn.execute('''
            INSERT INTO positions (symbol, action, quantity, entry_price, current_price, entry_time, pnl, pnl_percent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            position.symbol, position.action, position.quantity, position.entry_price,
            position.current_price, position.entry_time.isoformat(), position.pnl, position.pnl_percent
        ))
        conn.commit()
        conn.close()
    
    def _save_trade(self, symbol: str, action: str, quantity: float, price: float, pnl: float, reasoning: str):
        """Save completed trade to database"""
        conn = sqlite3.connect('paper_trading_simplified.db')
        conn.execute('''
            INSERT INTO trades (symbol, action, quantity, price, timestamp, pnl, reasoning)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (symbol, action, quantity, price, datetime.now().isoformat(), pnl, reasoning))
        conn.commit()
        conn.close()
        
        # Update performance metrics
        self.performance_metrics['total_trades'] += 1
        if pnl > 0:
            self.performance_metrics['winning_trades'] += 1
        else:
            self.performance_metrics['losing_trades'] += 1
        
        if self.performance_metrics['total_trades'] > 0:
            self.performance_metrics['win_rate'] = (
                self.performance_metrics['winning_trades'] / self.performance_metrics['total_trades']
            ) * 100
    
    def display_portfolio_status(self):
        """Display current portfolio status"""
        # Portfolio summary
        total_position_value = sum(pos.quantity * pos.current_price for pos in self.positions)
        total_portfolio_value = self.current_capital + total_position_value
        total_pnl = sum(pos.pnl for pos in self.positions) + self.performance_metrics['total_pnl']
        total_pnl_percent = (total_pnl / self.initial_capital) * 100
        
        # Create portfolio table
        portfolio_table = Table(title="📊 Portfolio Status")
        portfolio_table.add_column("Metric", style="cyan")
        portfolio_table.add_column("Value", style="green")
        
        portfolio_table.add_row("Initial Capital", f"${self.initial_capital:,.2f}")
        portfolio_table.add_row("Available Cash", f"${self.current_capital:,.2f}")
        portfolio_table.add_row("Position Value", f"${total_position_value:,.2f}")
        portfolio_table.add_row("Total Portfolio", f"${total_portfolio_value:,.2f}")
        portfolio_table.add_row("Total P&L", f"${total_pnl:,.2f} ({total_pnl_percent:+.2f}%)")
        portfolio_table.add_row("Open Positions", str(len(self.positions)))
        portfolio_table.add_row("Total Trades", str(self.performance_metrics['total_trades']))
        portfolio_table.add_row("Win Rate", f"{self.performance_metrics['win_rate']:.1f}%")
        
        console.print(portfolio_table)
        
        # Positions table
        if self.positions:
            positions_table = Table(title="📈 Open Positions")
            positions_table.add_column("Symbol", style="cyan")
            positions_table.add_column("Action", style="yellow")
            positions_table.add_column("Quantity", style="blue")
            positions_table.add_column("Entry Price", style="magenta")
            positions_table.add_column("Current Price", style="magenta")
            positions_table.add_column("P&L", style="green")
            positions_table.add_column("P&L %", style="green")
            
            for pos in self.positions:
                pnl_color = "green" if pos.pnl >= 0 else "red"
                positions_table.add_row(
                    pos.symbol,
                    pos.action,
                    f"{pos.quantity:.4f}",
                    f"${pos.entry_price:.2f}",
                    f"${pos.current_price:.2f}",
                    f"[{pnl_color}]${pos.pnl:.2f}[/{pnl_color}]",
                    f"[{pnl_color}]{pos.pnl_percent:+.2f}%[/{pnl_color}]"
                )
            
            console.print(positions_table)
    
    async def run_trading_session(self, duration_minutes: int = 30, trade_interval: int = 60):
        """Run automated paper trading session"""
        console.print(Panel(
            f"[bold blue]🚀 STARTING PAPER TRADING SESSION[/bold blue]\n\n"
            f"Duration: {duration_minutes} minutes\n"
            f"Trade Interval: {trade_interval} seconds\n"
            f"Available Symbols: {list(self.market_data.keys())}\n"
            f"Initial Capital: ${self.initial_capital:,.2f}",
            title="Trading Session"
        ))
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        symbols = list(self.market_data.keys())
        trade_count = 0
        
        while datetime.now() < end_time:
            # Update positions
            self.update_positions()
            
            # Get AI signal for random symbol
            import random
            symbol = random.choice(symbols)
            signal = self.get_ai_trading_signal(symbol)
            
            # Execute trade if signal is strong enough
            if signal.confidence >= 0.75:  # Only trade on high confidence
                if self.execute_trade(signal):
                    trade_count += 1
            
            # Display status
            console.clear()
            console.print(f"[bold blue]📊 LIVE PAPER TRADING SESSION[/bold blue]")
            console.print(f"Time Remaining: {(end_time - datetime.now()).total_seconds()/60:.1f} minutes")
            console.print(f"Trades Executed: {trade_count}")
            console.print()
            
            self.display_portfolio_status()
            
            # Wait for next trade interval
            await asyncio.sleep(min(trade_interval, 10))  # Max 10 second updates for demo
        
        console.print(Panel(
            f"[bold green]✅ TRADING SESSION COMPLETED[/bold green]\n\n"
            f"Duration: {duration_minutes} minutes\n"
            f"Trades Executed: {trade_count}\n"
            f"Final Portfolio Value: ${self.current_capital + sum(pos.quantity * pos.current_price for pos in self.positions):,.2f}\n"
            f"Session P&L: ${sum(pos.pnl for pos in self.positions):.2f}",
            title="Session Complete"
        ))

async def main():
    """Main paper trading function"""
    console.print("[bold blue]🚀 NORYON AI PAPER TRADING SYSTEM[/bold blue]\n")
    
    # Initialize paper trading
    paper_trader = SimplifiedPaperTrading(initial_capital=100000.0)
    
    # Run trading session
    await paper_trader.run_trading_session(duration_minutes=5, trade_interval=30)

if __name__ == "__main__":
    asyncio.run(main())
