#!/usr/bin/env python3
"""
Comprehensive Testing Framework for Noryon LLM Integration

This module provides:
- Unit tests for LLM abstraction layer
- Integration tests for multiple LLM providers
- Mock LLM server for testing
- Performance benchmarking
- Cost analysis testing
- Data pipeline testing
- End-to-end workflow testing
"""

import asyncio
import json
import logging
import os
import tempfile
import time
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, AsyncMock

import aiohttp
import pytest
import yaml
from aiohttp import web
from aiohttp.test_utils import AioHTTPTestCase, unittest_run_loop

# Import our modules
sys_path = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(sys_path))

from core.llm.llm_abstraction_layer import (
    LLMAbstractionLayer, LLMRequest, LLMResponse, LLMProvider,
    OpenAIProvider, LocalModelProvider, LLMCache
)
from core.data.data_manager import (
    DataManager, DataPoint, DataBatch, DataType, DataSource,
    YahooFinanceSource, BinanceSource, NewsAPISource
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockLLMServer:
    """Mock LLM server for testing"""
    
    def __init__(self, port: int = 8888):
        self.port = port
        self.app = web.Application()
        self.setup_routes()
        self.request_count = 0
        self.responses = {
            'default': {
                'choices': [{
                    'message': {
                        'content': 'This is a mock response for testing purposes.'
                    }
                }],
                'usage': {
                    'prompt_tokens': 10,
                    'completion_tokens': 15,
                    'total_tokens': 25
                }
            }
        }
    
    def setup_routes(self):
        """Setup mock API routes"""
        self.app.router.add_post('/chat/completions', self.chat_completions)
        self.app.router.add_post('/generate', self.local_generate)
        self.app.router.add_post('/generate_stream', self.local_generate_stream)
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/models', self.list_models)
    
    async def chat_completions(self, request):
        """Mock OpenAI chat completions endpoint"""
        self.request_count += 1
        
        try:
            data = await request.json()
            
            # Simulate processing delay
            await asyncio.sleep(0.1)
            
            # Check for specific test scenarios
            messages = data.get('messages', [])
            if messages and 'error' in messages[-1].get('content', '').lower():
                return web.json_response(
                    {'error': 'Mock error for testing'},
                    status=400
                )
            
            response = self.responses.get('default').copy()
            
            # Customize response based on request
            if messages:
                user_message = messages[-1].get('content', '')
                response['choices'][0]['message']['content'] = f"Mock analysis of: {user_message[:50]}..."
            
            return web.json_response(response)
        
        except Exception as e:
            return web.json_response(
                {'error': f'Mock server error: {str(e)}'},
                status=500
            )
    
    async def local_generate(self, request):
        """Mock local model generate endpoint"""
        self.request_count += 1
        
        try:
            data = await request.json()
            prompt = data.get('prompt', '')
            
            await asyncio.sleep(0.2)  # Simulate local model processing
            
            response = {
                'text': f"Local model response to: {prompt[:50]}...",
                'input_tokens': len(prompt.split()),
                'output_tokens': 20,
                'total_tokens': len(prompt.split()) + 20
            }
            
            return web.json_response(response)
        
        except Exception as e:
            return web.json_response(
                {'error': f'Local model error: {str(e)}'},
                status=500
            )
    
    async def local_generate_stream(self, request):
        """Mock local model streaming endpoint"""
        try:
            data = await request.json()
            prompt = data.get('prompt', '')
            
            response = web.StreamResponse()
            response.headers['Content-Type'] = 'application/json'
            await response.prepare(request)
            
            # Send streaming chunks
            words = f"Streaming response to {prompt[:30]}".split()
            for word in words:
                chunk = json.dumps({'text': word + ' '})
                await response.write(chunk.encode() + b'\n')
                await asyncio.sleep(0.1)
            
            await response.write_eof()
            return response
        
        except Exception as e:
            return web.json_response(
                {'error': f'Streaming error: {str(e)}'},
                status=500
            )
    
    async def health_check(self, request):
        """Mock health check endpoint"""
        return web.json_response({'status': 'healthy', 'requests_served': self.request_count})
    
    async def list_models(self, request):
        """Mock models list endpoint"""
        return web.json_response({
            'data': [
                {'id': 'gpt-4-turbo-preview', 'object': 'model'},
                {'id': 'gpt-3.5-turbo', 'object': 'model'}
            ]
        })
    
    def set_custom_response(self, scenario: str, response: Dict[str, Any]):
        """Set custom response for testing scenarios"""
        self.responses[scenario] = response
    
    async def start(self):
        """Start the mock server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()
        logger.info(f"Mock LLM server started on port {self.port}")
        return runner
    
    async def stop(self, runner):
        """Stop the mock server"""
        await runner.cleanup()
        logger.info("Mock LLM server stopped")

class TestLLMAbstractionLayer(unittest.TestCase):
    """Test cases for LLM abstraction layer"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config = {
            'llm_providers': {
                'openai': {
                    'enabled': True,
                    'api_key': 'test-key',
                    'base_url': 'http://localhost:8888',
                    'model': 'gpt-4-turbo-preview',
                    'max_tokens': 1000,
                    'timeout': 30,
                    'cost_per_1k_tokens': {
                        'input': 0.01,
                        'output': 0.03
                    }
                },
                'qwen_local': {
                    'enabled': True,
                    'type': 'local',
                    'model_path': '/path/to/qwen',
                    'model_name': 'qwen-7b',
                    'inference_url': 'http://localhost:8888',
                    'max_tokens': 2000,
                    'provider_name': 'qwen_local'
                }
            },
            'fallback_order': ['openai', 'qwen_local'],
            'redis_url': 'redis://localhost:6379'
        }
        
        # Create temporary config file
        self.config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.config_file)
        self.config_file.close()
        
        self.mock_server = MockLLMServer()
    
    def tearDown(self):
        """Clean up test environment"""
        os.unlink(self.config_file.name)
    
    async def asyncSetUp(self):
        """Async setup"""
        self.server_runner = await self.mock_server.start()
        await asyncio.sleep(0.1)  # Give server time to start
    
    async def asyncTearDown(self):
        """Async teardown"""
        await self.mock_server.stop(self.server_runner)
    
    @pytest.mark.asyncio
    async def test_llm_initialization(self):
        """Test LLM abstraction layer initialization"""
        await self.asyncSetUp()
        
        try:
            llm = LLMAbstractionLayer(self.config_file.name)
            
            # Check providers are initialized
            self.assertIn('openai', llm.providers)
            self.assertIn('qwen_local', llm.providers)
            
            # Check provider status
            status = await llm.get_provider_status()
            self.assertIsInstance(status, dict)
            
            await llm.close()
        
        finally:
            await self.asyncTearDown()
    
    @pytest.mark.asyncio
    async def test_openai_provider(self):
        """Test OpenAI provider functionality"""
        await self.asyncSetUp()
        
        try:
            llm = LLMAbstractionLayer(self.config_file.name)
            
            request = LLMRequest(
                prompt="Analyze AAPL stock performance",
                system_prompt="You are a financial analyst",
                max_tokens=500,
                temperature=0.1
            )
            
            response = await llm.generate(request, preferred_provider='openai')
            
            # Verify response structure
            self.assertIsInstance(response, LLMResponse)
            self.assertEqual(response.provider, LLMProvider.OPENAI)
            self.assertIsInstance(response.content, str)
            self.assertGreater(len(response.content), 0)
            self.assertIsInstance(response.tokens_used, dict)
            self.assertIsInstance(response.cost, float)
            self.assertIsInstance(response.latency, float)
            
            await llm.close()
        
        finally:
            await self.asyncTearDown()
    
    @pytest.mark.asyncio
    async def test_local_provider(self):
        """Test local model provider functionality"""
        await self.asyncSetUp()
        
        try:
            llm = LLMAbstractionLayer(self.config_file.name)
            
            request = LLMRequest(
                prompt="What are the current market trends?",
                max_tokens=300,
                temperature=0.2
            )
            
            response = await llm.generate(request, preferred_provider='qwen_local')
            
            # Verify response
            self.assertIsInstance(response, LLMResponse)
            self.assertIsInstance(response.content, str)
            self.assertEqual(response.cost, 0.0)  # Local models have no API cost
            
            await llm.close()
        
        finally:
            await self.asyncTearDown()
    
    @pytest.mark.asyncio
    async def test_fallback_mechanism(self):
        """Test provider fallback mechanism"""
        await self.asyncSetUp()
        
        try:
            # Configure mock server to fail for OpenAI
            self.mock_server.set_custom_response('openai_fail', {
                'error': 'Service unavailable'
            })
            
            llm = LLMAbstractionLayer(self.config_file.name)
            
            request = LLMRequest(
                prompt="error test",  # This will trigger error in mock
                max_tokens=100
            )
            
            # Should fallback to local provider
            response = await llm.generate(request, preferred_provider='openai')
            
            # Should get response from fallback provider
            self.assertIsInstance(response, LLMResponse)
            
            await llm.close()
        
        finally:
            await self.asyncTearDown()
    
    @pytest.mark.asyncio
    async def test_streaming_response(self):
        """Test streaming response functionality"""
        await self.asyncSetUp()
        
        try:
            llm = LLMAbstractionLayer(self.config_file.name)
            
            request = LLMRequest(
                prompt="Generate a market analysis report",
                stream=True
            )
            
            chunks = []
            async for chunk in llm.generate_stream(request, preferred_provider='qwen_local'):
                chunks.append(chunk)
            
            # Verify streaming worked
            self.assertGreater(len(chunks), 0)
            full_response = ''.join(chunks)
            self.assertGreater(len(full_response), 0)
            
            await llm.close()
        
        finally:
            await self.asyncTearDown()
    
    @pytest.mark.asyncio
    async def test_cost_estimation(self):
        """Test cost estimation functionality"""
        await self.asyncSetUp()
        
        try:
            llm = LLMAbstractionLayer(self.config_file.name)
            
            request = LLMRequest(
                prompt="This is a test prompt for cost estimation",
                max_tokens=500
            )
            
            # Test cost estimation
            estimated_cost = llm.get_cost_estimate(request, 'openai')
            self.assertIsInstance(estimated_cost, float)
            self.assertGreaterEqual(estimated_cost, 0.0)
            
            # Local model should have zero cost
            local_cost = llm.get_cost_estimate(request, 'qwen_local')
            self.assertEqual(local_cost, 0.0)
            
            await llm.close()
        
        finally:
            await self.asyncTearDown()

class TestDataManager(unittest.TestCase):
    """Test cases for data management system"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_data_config = {
            'data_sources': {
                'yahoo_finance': {
                    'enabled': True,
                    'source': 'yahoo_finance',
                    'rate_limit': {'requests_per_minute': 60}
                },
                'news_api': {
                    'enabled': True,
                    'source': 'news_api',
                    'api_key': 'test-news-key'
                }
            },
            'processing': {
                'batch_size': 100,
                'quality_threshold': 0.8
            },
            'storage': {
                'database_url': 'sqlite:///test.db',
                'redis_url': 'redis://localhost:6379',
                'file_storage_path': 'test_data'
            },
            'batch_size': 50,
            'processing_interval': 30
        }
        
        # Create temporary config file
        self.data_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_data_config, self.data_config_file)
        self.data_config_file.close()
    
    def tearDown(self):
        """Clean up test environment"""
        os.unlink(self.data_config_file.name)
        
        # Clean up test data directory
        test_data_path = Path('test_data')
        if test_data_path.exists():
            import shutil
            shutil.rmtree(test_data_path)
    
    @patch('yfinance.Ticker')
    def test_yahoo_finance_source(self, mock_ticker):
        """Test Yahoo Finance data source"""
        # Mock Yahoo Finance response
        mock_hist = Mock()
        mock_hist.iterrows.return_value = [
            (datetime(2024, 1, 1), {
                'Open': 150.0,
                'High': 155.0,
                'Low': 148.0,
                'Close': 152.0,
                'Volume': 1000000
            })
        ]
        mock_ticker.return_value.history.return_value = mock_hist
        
        source = YahooFinanceSource({'source': 'yahoo_finance', 'enabled': True})
        
        # Test data fetching
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            data_points = loop.run_until_complete(
                source.fetch_data(['AAPL'], datetime(2024, 1, 1), datetime(2024, 1, 2))
            )
            
            # Verify data points
            self.assertGreater(len(data_points), 0)
            
            # Check data point structure
            for dp in data_points:
                self.assertIsInstance(dp, DataPoint)
                self.assertEqual(dp.symbol, 'AAPL')
                self.assertEqual(dp.source, DataSource.YAHOO_FINANCE)
                self.assertIn(dp.data_type, [DataType.PRICE_DATA, DataType.VOLUME_DATA])
        
        finally:
            loop.close()
    
    def test_data_point_creation(self):
        """Test data point creation and validation"""
        data_point = DataPoint(
            timestamp=datetime.now(),
            symbol='AAPL',
            data_type=DataType.PRICE_DATA,
            source=DataSource.YAHOO_FINANCE,
            value=150.0,
            metadata={'price_type': 'close'},
            quality_score=1.0
        )
        
        # Verify data point
        self.assertEqual(data_point.symbol, 'AAPL')
        self.assertEqual(data_point.data_type, DataType.PRICE_DATA)
        self.assertEqual(data_point.source, DataSource.YAHOO_FINANCE)
        self.assertEqual(data_point.value, 150.0)
        self.assertEqual(data_point.quality_score, 1.0)
    
    def test_data_batch_creation(self):
        """Test data batch creation"""
        data_points = [
            DataPoint(
                timestamp=datetime.now(),
                symbol='AAPL',
                data_type=DataType.PRICE_DATA,
                source=DataSource.YAHOO_FINANCE,
                value=150.0
            ),
            DataPoint(
                timestamp=datetime.now(),
                symbol='GOOGL',
                data_type=DataType.PRICE_DATA,
                source=DataSource.YAHOO_FINANCE,
                value=2500.0
            )
        ]
        
        batch = DataBatch(
            data_points=data_points,
            batch_id='test_batch_001'
        )
        
        # Verify batch
        self.assertEqual(len(batch.data_points), 2)
        self.assertEqual(batch.batch_id, 'test_batch_001')
        self.assertIsInstance(batch.created_at, datetime)

class TestPerformanceBenchmarks(unittest.TestCase):
    """Performance benchmarking tests"""
    
    def setUp(self):
        """Set up benchmark environment"""
        self.mock_server = MockLLMServer(port=8889)
        
        self.benchmark_config = {
            'llm_providers': {
                'openai': {
                    'enabled': True,
                    'api_key': 'test-key',
                    'base_url': 'http://localhost:8889',
                    'model': 'gpt-4-turbo-preview',
                    'cost_per_1k_tokens': {'input': 0.01, 'output': 0.03}
                }
            },
            'fallback_order': ['openai']
        }
        
        self.config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.benchmark_config, self.config_file)
        self.config_file.close()
    
    def tearDown(self):
        """Clean up benchmark environment"""
        os.unlink(self.config_file.name)
    
    @pytest.mark.asyncio
    async def test_latency_benchmark(self):
        """Benchmark LLM response latency"""
        server_runner = await self.mock_server.start()
        
        try:
            llm = LLMAbstractionLayer(self.config_file.name)
            
            # Test multiple requests
            latencies = []
            num_requests = 10
            
            for i in range(num_requests):
                request = LLMRequest(
                    prompt=f"Test request {i}: Analyze market conditions",
                    max_tokens=100
                )
                
                start_time = time.time()
                response = await llm.generate(request)
                latency = time.time() - start_time
                
                latencies.append(latency)
                self.assertIsInstance(response, LLMResponse)
            
            # Calculate statistics
            avg_latency = sum(latencies) / len(latencies)
            max_latency = max(latencies)
            min_latency = min(latencies)
            
            logger.info(f"Latency Benchmark Results:")
            logger.info(f"  Average: {avg_latency:.3f}s")
            logger.info(f"  Min: {min_latency:.3f}s")
            logger.info(f"  Max: {max_latency:.3f}s")
            
            # Assert reasonable performance
            self.assertLess(avg_latency, 5.0)  # Should be under 5 seconds
            
            await llm.close()
        
        finally:
            await self.mock_server.stop(server_runner)
    
    @pytest.mark.asyncio
    async def test_throughput_benchmark(self):
        """Benchmark concurrent request throughput"""
        server_runner = await self.mock_server.start()
        
        try:
            llm = LLMAbstractionLayer(self.config_file.name)
            
            # Test concurrent requests
            num_concurrent = 5
            requests = [
                LLMRequest(
                    prompt=f"Concurrent request {i}: Market analysis",
                    max_tokens=50
                )
                for i in range(num_concurrent)
            ]
            
            start_time = time.time()
            
            # Execute concurrent requests
            tasks = [
                llm.generate(request)
                for request in requests
            ]
            
            responses = await asyncio.gather(*tasks)
            
            total_time = time.time() - start_time
            throughput = len(responses) / total_time
            
            logger.info(f"Throughput Benchmark Results:")
            logger.info(f"  Requests: {len(responses)}")
            logger.info(f"  Total time: {total_time:.3f}s")
            logger.info(f"  Throughput: {throughput:.2f} requests/second")
            
            # Verify all requests completed
            self.assertEqual(len(responses), num_concurrent)
            for response in responses:
                self.assertIsInstance(response, LLMResponse)
            
            await llm.close()
        
        finally:
            await self.mock_server.stop(server_runner)

class TestEndToEndWorkflow(unittest.TestCase):
    """End-to-end workflow testing"""
    
    def setUp(self):
        """Set up end-to-end test environment"""
        self.mock_server = MockLLMServer(port=8890)
        
        # Complete system configuration
        self.system_config = {
            'llm_providers': {
                'openai': {
                    'enabled': True,
                    'api_key': 'test-key',
                    'base_url': 'http://localhost:8890',
                    'model': 'gpt-4-turbo-preview',
                    'cost_per_1k_tokens': {'input': 0.01, 'output': 0.03}
                }
            },
            'fallback_order': ['openai']
        }
        
        self.data_config = {
            'data_sources': {
                'yahoo_finance': {
                    'enabled': True,
                    'source': 'yahoo_finance'
                }
            },
            'storage': {
                'database_url': 'sqlite:///e2e_test.db',
                'file_storage_path': 'e2e_test_data'
            }
        }
        
        # Create config files
        self.llm_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.system_config, self.llm_config_file)
        self.llm_config_file.close()
        
        self.data_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.data_config, self.data_config_file)
        self.data_config_file.close()
    
    def tearDown(self):
        """Clean up end-to-end test environment"""
        os.unlink(self.llm_config_file.name)
        os.unlink(self.data_config_file.name)
        
        # Clean up test data
        for path in ['e2e_test.db', 'e2e_test_data']:
            if os.path.exists(path):
                if os.path.isdir(path):
                    import shutil
                    shutil.rmtree(path)
                else:
                    os.unlink(path)
    
    @pytest.mark.asyncio
    async def test_complete_trading_workflow(self):
        """Test complete trading workflow from data to decision"""
        server_runner = await self.mock_server.start()
        
        try:
            # Initialize systems
            llm = LLMAbstractionLayer(self.llm_config_file.name)
            data_manager = DataManager(self.data_config_file.name)
            
            # Step 1: Check system health
            llm_status = await llm.get_provider_status()
            data_status = await data_manager.health_check()
            
            logger.info(f"LLM Status: {llm_status}")
            logger.info(f"Data Status: {data_status}")
            
            # Step 2: Simulate data collection
            symbols = ['AAPL', 'GOOGL']
            
            # Mock some market data
            mock_data_points = [
                DataPoint(
                    timestamp=datetime.now(),
                    symbol='AAPL',
                    data_type=DataType.PRICE_DATA,
                    source=DataSource.YAHOO_FINANCE,
                    value=150.0,
                    metadata={'price_type': 'close'}
                ),
                DataPoint(
                    timestamp=datetime.now(),
                    symbol='GOOGL',
                    data_type=DataType.PRICE_DATA,
                    source=DataSource.YAHOO_FINANCE,
                    value=2500.0,
                    metadata={'price_type': 'close'}
                )
            ]
            
            # Step 3: Process data
            batch = DataBatch(
                data_points=mock_data_points,
                batch_id='e2e_test_batch'
            )
            
            processed_batch = await data_manager.processor.process_batch(batch)
            
            # Step 4: Generate trading analysis using LLM
            market_data_summary = f"AAPL: $150.00, GOOGL: $2500.00"
            
            analysis_request = LLMRequest(
                prompt=f"Analyze the following market data and provide trading recommendations: {market_data_summary}",
                system_prompt="You are a professional trading analyst. Provide concise, actionable trading recommendations.",
                max_tokens=500,
                temperature=0.1
            )
            
            analysis_response = await llm.generate(analysis_request)
            
            # Step 5: Verify workflow completion
            self.assertIsInstance(processed_batch, DataBatch)
            self.assertEqual(len(processed_batch.data_points), 2)
            
            self.assertIsInstance(analysis_response, LLMResponse)
            self.assertGreater(len(analysis_response.content), 0)
            
            logger.info(f"Trading Analysis: {analysis_response.content[:100]}...")
            logger.info(f"Analysis Cost: ${analysis_response.cost:.4f}")
            logger.info(f"Analysis Latency: {analysis_response.latency:.2f}s")
            
            # Clean up
            await llm.close()
        
        finally:
            await self.mock_server.stop(server_runner)

# Test runner and utilities
class TestRunner:
    """Test runner for all test suites"""
    
    def __init__(self):
        self.test_suites = [
            TestLLMAbstractionLayer,
            TestDataManager,
            TestPerformanceBenchmarks,
            TestEndToEndWorkflow
        ]
    
    async def run_all_tests(self):
        """Run all test suites"""
        logger.info("Starting comprehensive test suite...")
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_suite in self.test_suites:
            logger.info(f"Running {test_suite.__name__}...")
            
            suite = unittest.TestLoader().loadTestsFromTestCase(test_suite)
            runner = unittest.TextTestRunner(verbosity=2)
            result = runner.run(suite)
            
            total_tests += result.testsRun
            passed_tests += result.testsRun - len(result.failures) - len(result.errors)
            failed_tests += len(result.failures) + len(result.errors)
        
        logger.info(f"\nTest Results Summary:")
        logger.info(f"  Total tests: {total_tests}")
        logger.info(f"  Passed: {passed_tests}")
        logger.info(f"  Failed: {failed_tests}")
        logger.info(f"  Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        return failed_tests == 0
    
    def run_specific_test(self, test_class_name: str, test_method_name: Optional[str] = None):
        """Run a specific test or test class"""
        for test_suite in self.test_suites:
            if test_suite.__name__ == test_class_name:
                if test_method_name:
                    suite = unittest.TestSuite()
                    suite.addTest(test_suite(test_method_name))
                else:
                    suite = unittest.TestLoader().loadTestsFromTestCase(test_suite)
                
                runner = unittest.TextTestRunner(verbosity=2)
                result = runner.run(suite)
                return result
        
        logger.error(f"Test class {test_class_name} not found")
        return None

# CLI interface for running tests
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Noryon LLM Integration Test Suite')
    parser.add_argument('--test-class', help='Specific test class to run')
    parser.add_argument('--test-method', help='Specific test method to run')
    parser.add_argument('--benchmark', action='store_true', help='Run performance benchmarks')
    parser.add_argument('--e2e', action='store_true', help='Run end-to-end tests only')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.test_class:
        result = runner.run_specific_test(args.test_class, args.test_method)
    elif args.benchmark:
        result = runner.run_specific_test('TestPerformanceBenchmarks')
    elif args.e2e:
        result = runner.run_specific_test('TestEndToEndWorkflow')
    else:
        # Run all tests
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success = loop.run_until_complete(runner.run_all_tests())
            exit(0 if success else 1)
        finally:
            loop.close()