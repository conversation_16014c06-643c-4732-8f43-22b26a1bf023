
"""Windows-compatible subprocess wrapper"""
import subprocess
import platform
import logging

logger = logging.getLogger(__name__)

def run_command_safe(cmd, timeout=60, shell=None):
    """Safe command runner with Windows compatibility"""
    try:
        if isinstance(cmd, str):
            cmd = cmd.split()
        
        is_windows = platform.system() == 'Windows'
        
        if shell is None:
            shell = is_windows
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=timeout,
            shell=shell
        )
        return result
        
    except subprocess.TimeoutExpired:
        return subprocess.CompletedProcess(cmd, 1, "", "Timeout")
    except Exception as e:
        return subprocess.CompletedProcess(cmd, 1, "", str(e))

def check_ollama_available():
    """Check if Ollama is available"""
    try:
        result = run_command_safe(['ollama', '--version'], timeout=10)
        return result.returncode == 0
    except:
        return False
