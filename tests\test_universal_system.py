#!/usr/bin/env python3
"""
Noryon Trading AI System - Comprehensive Test Suite

This test suite validates the universal broker interface and ensures
all components work correctly across different broker types.
"""

import pytest
import asyncio
import json
import tempfile
import os
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime, timedelta

# Import core components
from core.interfaces.broker_interface import (
    UniversalBrokerInterface, OrderSide, OrderType, OrderStatus,
    TimeInForce, AssetType, UniversalTick, UniversalOrder,
    OrderResult, Position, AccountInfo, MarketData, HistoricalData
)
from core.registry.broker_registry import BrokerRegistry
from core.config.config_manager import ConfigManager
from core.portfolio.universal_portfolio_manager import UniversalPortfolioManager

# Import adapters
from adapters.crypto.binance import BinanceAdapter
from adapters.traditional.interactive_brokers import InteractiveBrokersAdapter


class MockBrokerAdapter(UniversalBrokerInterface):
    """Mock broker adapter for testing universal interface"""
    
    def __init__(self, broker_name: str = "mock_broker"):
        self.broker_name = broker_name
        self.is_connected = False
        self.orders = {}
        self.positions = {}
        self.account_balance = Decimal('10000.00')
        self.order_counter = 1
    
    async def connect(self) -> bool:
        """Mock connection"""
        await asyncio.sleep(0.1)  # Simulate connection delay
        self.is_connected = True
        return True
    
    async def disconnect(self) -> bool:
        """Mock disconnection"""
        self.is_connected = False
        return True
    
    async def is_connected_status(self) -> bool:
        """Check connection status"""
        return self.is_connected
    
    async def get_account_info(self) -> AccountInfo:
        """Mock account info"""
        return AccountInfo(
            account_id="MOCK123456",
            balance=self.account_balance,
            currency="USD",
            buying_power=self.account_balance * Decimal('4'),
            maintenance_margin=Decimal('0'),
            available_margin=self.account_balance * Decimal('4')
        )
    
    async def get_positions(self) -> list[Position]:
        """Mock positions"""
        return list(self.positions.values())
    
    async def get_market_data(self, symbol: str) -> MarketData:
        """Mock market data"""
        base_price = Decimal('100.00')
        if 'BTC' in symbol:
            base_price = Decimal('50000.00')
        elif 'ETH' in symbol:
            base_price = Decimal('3000.00')
        
        return MarketData(
            symbol=symbol,
            bid=base_price * Decimal('0.999'),
            ask=base_price * Decimal('1.001'),
            last=base_price,
            volume=Decimal('1000'),
            timestamp=datetime.now()
        )
    
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> HistoricalData:
        """Mock historical data"""
        # Generate mock OHLCV data
        data_points = []
        current_time = start_date
        base_price = Decimal('100.00')
        
        while current_time <= end_date:
            data_points.append({
                'timestamp': current_time,
                'open': base_price,
                'high': base_price * Decimal('1.02'),
                'low': base_price * Decimal('0.98'),
                'close': base_price * Decimal('1.01'),
                'volume': Decimal('1000')
            })
            current_time += timedelta(hours=1)
            base_price *= Decimal('1.001')  # Slight upward trend
        
        return HistoricalData(
            symbol=symbol,
            timeframe=timeframe,
            data=data_points
        )
    
    async def place_order(self, order: UniversalOrder) -> OrderResult:
        """Mock order placement"""
        order_id = f"MOCK_{self.order_counter}"
        self.order_counter += 1
        
        # Simulate order execution
        executed_price = order.price if order.price else Decimal('100.00')
        
        # Store order
        self.orders[order_id] = {
            'id': order_id,
            'symbol': order.symbol,
            'side': order.side,
            'quantity': order.quantity,
            'price': executed_price,
            'status': OrderStatus.FILLED,
            'timestamp': datetime.now()
        }
        
        # Update positions
        if order.symbol not in self.positions:
            self.positions[order.symbol] = Position(
                symbol=order.symbol,
                quantity=Decimal('0'),
                average_price=Decimal('0'),
                market_value=Decimal('0'),
                unrealized_pnl=Decimal('0'),
                side='LONG'
            )
        
        position = self.positions[order.symbol]
        if order.side == OrderSide.BUY:
            new_quantity = position.quantity + order.quantity
            if new_quantity != 0:
                position.average_price = (
                    (position.quantity * position.average_price + 
                     order.quantity * executed_price) / new_quantity
                )
            position.quantity = new_quantity
        else:  # SELL
            position.quantity -= order.quantity
        
        position.market_value = position.quantity * executed_price
        
        return OrderResult(
            order_id=order_id,
            status=OrderStatus.FILLED,
            filled_quantity=order.quantity,
            average_fill_price=executed_price,
            timestamp=datetime.now()
        )
    
    async def cancel_order(self, order_id: str) -> bool:
        """Mock order cancellation"""
        if order_id in self.orders:
            self.orders[order_id]['status'] = OrderStatus.CANCELLED
            return True
        return False
    
    async def get_order_status(self, order_id: str) -> OrderResult:
        """Mock order status"""
        if order_id in self.orders:
            order = self.orders[order_id]
            return OrderResult(
                order_id=order_id,
                status=order['status'],
                filled_quantity=order['quantity'],
                average_fill_price=order['price'],
                timestamp=order['timestamp']
            )
        raise ValueError(f"Order {order_id} not found")
    
    async def subscribe_to_market_data(self, symbols: list[str], callback):
        """Mock market data subscription"""
        # Simulate real-time data
        for symbol in symbols:
            tick = UniversalTick(
                symbol=symbol,
                bid=Decimal('99.99'),
                ask=Decimal('100.01'),
                last=Decimal('100.00'),
                volume=Decimal('1000'),
                timestamp=datetime.now()
            )
            await callback(tick)


class TestUniversalBrokerInterface:
    """Test the universal broker interface"""
    
    @pytest.fixture
    def mock_broker(self):
        return MockBrokerAdapter()
    
    @pytest.mark.asyncio
    async def test_connection(self, mock_broker):
        """Test broker connection"""
        assert not await mock_broker.is_connected_status()
        
        result = await mock_broker.connect()
        assert result is True
        assert await mock_broker.is_connected_status()
        
        result = await mock_broker.disconnect()
        assert result is True
        assert not await mock_broker.is_connected_status()
    
    @pytest.mark.asyncio
    async def test_account_info(self, mock_broker):
        """Test account information retrieval"""
        await mock_broker.connect()
        
        account_info = await mock_broker.get_account_info()
        assert isinstance(account_info, AccountInfo)
        assert account_info.account_id == "MOCK123456"
        assert account_info.balance == Decimal('10000.00')
        assert account_info.currency == "USD"
    
    @pytest.mark.asyncio
    async def test_market_data(self, mock_broker):
        """Test market data retrieval"""
        await mock_broker.connect()
        
        market_data = await mock_broker.get_market_data("BTCUSDT")
        assert isinstance(market_data, MarketData)
        assert market_data.symbol == "BTCUSDT"
        assert market_data.bid < market_data.ask
        assert market_data.last > 0
    
    @pytest.mark.asyncio
    async def test_historical_data(self, mock_broker):
        """Test historical data retrieval"""
        await mock_broker.connect()
        
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now()
        
        historical_data = await mock_broker.get_historical_data(
            "BTCUSDT", "1h", start_date, end_date
        )
        
        assert isinstance(historical_data, HistoricalData)
        assert historical_data.symbol == "BTCUSDT"
        assert len(historical_data.data) > 0
        assert all('timestamp' in point for point in historical_data.data)
    
    @pytest.mark.asyncio
    async def test_order_placement(self, mock_broker):
        """Test order placement and execution"""
        await mock_broker.connect()
        
        order = UniversalOrder(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('0.001'),
            time_in_force=TimeInForce.GTC
        )
        
        result = await mock_broker.place_order(order)
        assert isinstance(result, OrderResult)
        assert result.status == OrderStatus.FILLED
        assert result.filled_quantity == order.quantity
        
        # Check order status
        order_status = await mock_broker.get_order_status(result.order_id)
        assert order_status.status == OrderStatus.FILLED
    
    @pytest.mark.asyncio
    async def test_position_tracking(self, mock_broker):
        """Test position tracking after orders"""
        await mock_broker.connect()
        
        # Place buy order
        buy_order = UniversalOrder(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('0.001')
        )
        
        await mock_broker.place_order(buy_order)
        
        # Check positions
        positions = await mock_broker.get_positions()
        assert len(positions) > 0
        
        btc_position = next((p for p in positions if p.symbol == "BTCUSDT"), None)
        assert btc_position is not None
        assert btc_position.quantity == Decimal('0.001')
    
    @pytest.mark.asyncio
    async def test_order_cancellation(self, mock_broker):
        """Test order cancellation"""
        await mock_broker.connect()
        
        # For this test, we'll modify the mock to not immediately fill orders
        original_place_order = mock_broker.place_order
        
        async def place_pending_order(order):
            result = await original_place_order(order)
            # Change status to pending
            mock_broker.orders[result.order_id]['status'] = OrderStatus.PENDING
            result.status = OrderStatus.PENDING
            return result
        
        mock_broker.place_order = place_pending_order
        
        order = UniversalOrder(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=Decimal('0.001'),
            price=Decimal('45000.00')
        )
        
        result = await mock_broker.place_order(order)
        assert result.status == OrderStatus.PENDING
        
        # Cancel the order
        cancel_result = await mock_broker.cancel_order(result.order_id)
        assert cancel_result is True
        
        # Check order status
        order_status = await mock_broker.get_order_status(result.order_id)
        assert order_status.status == OrderStatus.CANCELLED


class TestBrokerRegistry:
    """Test the broker registry system"""
    
    @pytest.fixture
    def temp_config_dir(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def broker_registry(self, temp_config_dir):
        return BrokerRegistry(config_dir=temp_config_dir)
    
    def test_broker_registration(self, broker_registry):
        """Test manual broker registration"""
        mock_broker = MockBrokerAdapter("test_broker")
        
        broker_registry.register_broker("test_broker", mock_broker)
        
        assert "test_broker" in broker_registry.get_registered_brokers()
        assert broker_registry.get_broker("test_broker") == mock_broker
    
    def test_broker_unregistration(self, broker_registry):
        """Test broker unregistration"""
        mock_broker = MockBrokerAdapter("test_broker")
        
        broker_registry.register_broker("test_broker", mock_broker)
        assert "test_broker" in broker_registry.get_registered_brokers()
        
        broker_registry.unregister_broker("test_broker")
        assert "test_broker" not in broker_registry.get_registered_brokers()
    
    @pytest.mark.asyncio
    async def test_broker_health_check(self, broker_registry):
        """Test broker health checking"""
        mock_broker = MockBrokerAdapter("test_broker")
        broker_registry.register_broker("test_broker", mock_broker)
        
        # Broker not connected initially
        health_status = await broker_registry.check_broker_health("test_broker")
        assert health_status is False
        
        # Connect broker
        await mock_broker.connect()
        health_status = await broker_registry.check_broker_health("test_broker")
        assert health_status is True
    
    def test_registry_statistics(self, broker_registry):
        """Test registry statistics"""
        # Register multiple brokers
        for i in range(3):
            broker = MockBrokerAdapter(f"broker_{i}")
            broker_registry.register_broker(f"broker_{i}", broker)
        
        stats = broker_registry.get_registry_stats()
        assert stats['total_brokers'] == 3
        assert stats['connected_brokers'] == 0  # None connected yet


class TestConfigManager:
    """Test the configuration manager"""
    
    @pytest.fixture
    def temp_config_dir(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def config_manager(self, temp_config_dir):
        return ConfigManager(config_dir=temp_config_dir, environment="test")
    
    def test_config_initialization(self, config_manager):
        """Test configuration manager initialization"""
        assert config_manager.environment == "test"
        assert config_manager.config_dir is not None
    
    def test_broker_credentials_management(self, config_manager):
        """Test broker credentials storage and retrieval"""
        credentials = {
            'api_key': 'test_api_key',
            'api_secret': 'test_api_secret'
        }
        
        # Save credentials
        config_manager.save_broker_credentials("test_broker", credentials)
        
        # Retrieve credentials
        retrieved_creds = config_manager.get_broker_credentials("test_broker")
        assert retrieved_creds['api_key'] == 'test_api_key'
        assert retrieved_creds['api_secret'] == 'test_api_secret'
    
    def test_trading_config_management(self, config_manager):
        """Test trading configuration management"""
        trading_config = {
            'max_position_size': 0.02,
            'risk_per_trade': 0.01,
            'max_daily_loss': 0.05
        }
        
        config_manager.update_trading_config(trading_config)
        retrieved_config = config_manager.get_trading_config()
        
        assert retrieved_config.max_position_size == 0.02
        assert retrieved_config.risk_per_trade == 0.01
        assert retrieved_config.max_daily_loss == 0.05


class TestUniversalPortfolioManager:
    """Test the universal portfolio manager"""
    
    @pytest.fixture
    def temp_config_dir(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def config_manager(self, temp_config_dir):
        return ConfigManager(config_dir=temp_config_dir, environment="test")
    
    @pytest.fixture
    def portfolio_manager(self, config_manager):
        return UniversalPortfolioManager(config_manager)
    
    @pytest.mark.asyncio
    async def test_broker_registration(self, portfolio_manager):
        """Test broker registration with portfolio manager"""
        mock_broker = MockBrokerAdapter("test_broker")
        await mock_broker.connect()
        
        portfolio_manager.register_broker("test_broker", mock_broker)
        
        assert "test_broker" in portfolio_manager.brokers
        assert portfolio_manager.brokers["test_broker"] == mock_broker
    
    @pytest.mark.asyncio
    async def test_cross_broker_position_sync(self, portfolio_manager):
        """Test position synchronization across brokers"""
        # Register multiple mock brokers
        broker1 = MockBrokerAdapter("broker1")
        broker2 = MockBrokerAdapter("broker2")
        
        await broker1.connect()
        await broker2.connect()
        
        portfolio_manager.register_broker("broker1", broker1)
        portfolio_manager.register_broker("broker2", broker2)
        
        # Place orders on different brokers
        order1 = UniversalOrder(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('0.001')
        )
        
        order2 = UniversalOrder(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('0.002')
        )
        
        await broker1.place_order(order1)
        await broker2.place_order(order2)
        
        # Sync positions
        await portfolio_manager.sync_positions()
        
        # Check consolidated positions
        positions = portfolio_manager.get_all_positions()
        btc_positions = [p for p in positions if p.symbol == "BTCUSDT"]
        
        assert len(btc_positions) == 2  # One from each broker
        total_quantity = sum(p.quantity for p in btc_positions)
        assert total_quantity == Decimal('0.003')
    
    @pytest.mark.asyncio
    async def test_smart_order_routing(self, portfolio_manager):
        """Test smart order routing functionality"""
        # Register multiple brokers with different characteristics
        broker1 = MockBrokerAdapter("low_fee_broker")
        broker2 = MockBrokerAdapter("high_liquidity_broker")
        
        await broker1.connect()
        await broker2.connect()
        
        portfolio_manager.register_broker("low_fee_broker", broker1)
        portfolio_manager.register_broker("high_liquidity_broker", broker2)
        
        # Configure routing preferences
        portfolio_manager.order_routing.preferred_brokers = {
            "BTCUSDT": "low_fee_broker"
        }
        
        # Place order through portfolio manager
        result = await portfolio_manager.place_order(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            quantity=Decimal('0.001'),
            order_type=OrderType.MARKET
        )
        
        assert result is not None
        assert result.status == OrderStatus.FILLED
        
        # Verify order was routed to preferred broker
        broker1_orders = broker1.orders
        assert len(broker1_orders) == 1
    
    @pytest.mark.asyncio
    async def test_risk_validation(self, portfolio_manager):
        """Test risk validation before order placement"""
        mock_broker = MockBrokerAdapter("test_broker")
        await mock_broker.connect()
        
        portfolio_manager.register_broker("test_broker", mock_broker)
        
        # Set strict risk limits
        portfolio_manager.risk_limits.max_position_size = Decimal('0.001')
        
        # Try to place order exceeding risk limits
        with pytest.raises(ValueError, match="exceeds maximum position size"):
            await portfolio_manager.place_order(
                symbol="BTCUSDT",
                side=OrderSide.BUY,
                quantity=Decimal('0.002'),  # Exceeds limit
                order_type=OrderType.MARKET
            )


class TestIntegrationScenarios:
    """Integration tests for complete trading scenarios"""
    
    @pytest.fixture
    def temp_config_dir(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    async def trading_system(self, temp_config_dir):
        """Set up complete trading system for integration tests"""
        # Initialize components
        config_manager = ConfigManager(config_dir=temp_config_dir, environment="test")
        portfolio_manager = UniversalPortfolioManager(config_manager)
        broker_registry = BrokerRegistry(config_dir=temp_config_dir)
        
        # Register mock brokers
        binance_mock = MockBrokerAdapter("binance")
        ib_mock = MockBrokerAdapter("interactive_brokers")
        
        await binance_mock.connect()
        await ib_mock.connect()
        
        broker_registry.register_broker("binance", binance_mock)
        broker_registry.register_broker("interactive_brokers", ib_mock)
        
        portfolio_manager.register_broker("binance", binance_mock)
        portfolio_manager.register_broker("interactive_brokers", ib_mock)
        
        return {
            'config_manager': config_manager,
            'portfolio_manager': portfolio_manager,
            'broker_registry': broker_registry,
            'brokers': {
                'binance': binance_mock,
                'interactive_brokers': ib_mock
            }
        }
    
    @pytest.mark.asyncio
    async def test_multi_broker_trading_scenario(self, trading_system):
        """Test complete multi-broker trading scenario"""
        portfolio_manager = trading_system['portfolio_manager']
        
        # Execute trades on multiple brokers
        trades = [
            {
                'symbol': 'BTCUSDT',
                'side': OrderSide.BUY,
                'quantity': Decimal('0.001'),
                'broker': 'binance'
            },
            {
                'symbol': 'AAPL',
                'side': OrderSide.BUY,
                'quantity': Decimal('10'),
                'broker': 'interactive_brokers'
            }
        ]
        
        results = []
        for trade in trades:
            # Set preferred broker for routing
            portfolio_manager.order_routing.preferred_brokers[trade['symbol']] = trade['broker']
            
            result = await portfolio_manager.place_order(
                symbol=trade['symbol'],
                side=trade['side'],
                quantity=trade['quantity'],
                order_type=OrderType.MARKET
            )
            results.append(result)
        
        # Verify all trades executed successfully
        assert all(r.status == OrderStatus.FILLED for r in results)
        
        # Sync and verify positions
        await portfolio_manager.sync_positions()
        positions = portfolio_manager.get_all_positions()
        
        assert len(positions) == 2
        symbols = {p.symbol for p in positions}
        assert 'BTCUSDT' in symbols
        assert 'AAPL' in symbols
    
    @pytest.mark.asyncio
    async def test_broker_failover_scenario(self, trading_system):
        """Test broker failover functionality"""
        portfolio_manager = trading_system['portfolio_manager']
        brokers = trading_system['brokers']
        
        # Simulate primary broker failure
        primary_broker = brokers['binance']
        secondary_broker = brokers['interactive_brokers']
        
        # Disconnect primary broker
        await primary_broker.disconnect()
        
        # Configure failover
        portfolio_manager.order_routing.enable_failover = True
        portfolio_manager.order_routing.fallback_brokers = ['interactive_brokers']
        
        # Attempt to place order (should failover to secondary broker)
        result = await portfolio_manager.place_order(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            quantity=Decimal('0.001'),
            order_type=OrderType.MARKET
        )
        
        assert result.status == OrderStatus.FILLED
        
        # Verify order was placed on secondary broker
        assert len(secondary_broker.orders) == 1
        assert len(primary_broker.orders) == 0
    
    @pytest.mark.asyncio
    async def test_portfolio_risk_management(self, trading_system):
        """Test portfolio-level risk management"""
        portfolio_manager = trading_system['portfolio_manager']
        
        # Set portfolio risk limits
        portfolio_manager.risk_limits.max_total_exposure = Decimal('5000.00')
        portfolio_manager.risk_limits.max_positions_per_symbol = 1
        
        # Place initial position
        await portfolio_manager.place_order(
            symbol="BTCUSDT",
            side=OrderSide.BUY,
            quantity=Decimal('0.001'),
            order_type=OrderType.MARKET
        )
        
        # Try to place another position in same symbol (should be rejected)
        with pytest.raises(ValueError, match="Maximum positions per symbol exceeded"):
            await portfolio_manager.place_order(
                symbol="BTCUSDT",
                side=OrderSide.BUY,
                quantity=Decimal('0.001'),
                order_type=OrderType.MARKET
            )


if __name__ == "__main__":
    # Run tests
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--asyncio-mode=auto"
    ])