# Noryon Trading AI - Broker Configuration Examples
# This file contains example configurations for various brokers
# Copy and modify these examples to create your own broker configurations

# =============================================================================
# CRYPTOCURRENCY EXCHANGES
# =============================================================================

# Binance Configuration
binance:
  name: "Binance"
  type: "crypto"
  adapter_class: "adapters.crypto.binance.BinanceAdapter"
  base_url: "https://api.binance.com"
  websocket_url: "wss://stream.binance.com:9443"
  
  # API Configuration
  api_config:
    rate_limit: 1200  # requests per minute
    timeout: 30
    retry_attempts: 3
    retry_delay: 1
  
  # Trading Configuration
  trading_config:
    min_order_size: 0.001
    max_order_size: 1000
    supported_order_types:
      - "MARKET"
      - "LIMIT"
      - "STOP_LOSS"
      - "STOP_LOSS_LIMIT"
      - "TAKE_PROFIT"
      - "TAKE_PROFIT_LIMIT"
    
    # Fee structure
    fees:
      maker: 0.001  # 0.1%
      taker: 0.001  # 0.1%
      withdrawal: 0.0005  # varies by asset
  
  # Supported assets
  supported_assets:
    - "BTCUSDT"
    - "ETHUSDT"
    - "ADAUSDT"
    - "DOTUSDT"
    - "LINKUSDT"
    - "BNBUSDT"
  
  # Risk management
  risk_config:
    max_position_size: 0.1  # 10% of account
    max_daily_trades: 50
    enable_stop_loss: true
    default_stop_loss: 0.02  # 2%

# Coinbase Pro Configuration
coinbase_pro:
  name: "Coinbase Pro"
  type: "crypto"
  adapter_class: "adapters.crypto.coinbase_pro.CoinbaseProAdapter"
  base_url: "https://api.pro.coinbase.com"
  websocket_url: "wss://ws-feed.pro.coinbase.com"
  
  api_config:
    rate_limit: 10  # requests per second
    timeout: 30
    retry_attempts: 3
  
  trading_config:
    min_order_size: 0.001
    max_order_size: 1000
    supported_order_types:
      - "market"
      - "limit"
      - "stop"
    
    fees:
      maker: 0.005  # 0.5%
      taker: 0.005  # 0.5%
  
  supported_assets:
    - "BTC-USD"
    - "ETH-USD"
    - "ADA-USD"
    - "DOT-USD"
    - "LINK-USD"

# =============================================================================
# TRADITIONAL BROKERS
# =============================================================================

# Interactive Brokers Configuration
interactive_brokers:
  name: "Interactive Brokers"
  type: "traditional"
  adapter_class: "adapters.traditional.interactive_brokers.InteractiveBrokersAdapter"
  
  # TWS/Gateway Configuration
  connection_config:
    host: "127.0.0.1"
    port: 7497  # TWS: 7497, Gateway: 4001
    client_id: 1
    timeout: 30
  
  # Account Configuration
  account_config:
    account_id: "DU123456"  # Demo account
    currency: "USD"
    account_type: "DEMO"  # DEMO or LIVE
  
  trading_config:
    min_order_size: 1
    max_order_size: 10000
    supported_order_types:
      - "MKT"   # Market
      - "LMT"   # Limit
      - "STP"   # Stop
      - "STP LMT"  # Stop Limit
    
    # Asset classes
    supported_asset_classes:
      - "STK"   # Stocks
      - "CASH"  # Forex
      - "FUT"   # Futures
      - "OPT"   # Options
    
    fees:
      stock_per_share: 0.005  # $0.005 per share
      forex_per_trade: 2.50   # $2.50 per trade
      minimum_per_order: 1.00 # $1.00 minimum
  
  # Market data subscriptions
  market_data:
    real_time: true
    level_2: false  # Requires additional subscription
    news: true

# Alpaca Configuration
alpaca:
  name: "Alpaca"
  type: "traditional"
  adapter_class: "adapters.traditional.alpaca.AlpacaAdapter"
  base_url: "https://paper-api.alpaca.markets"  # Paper trading
  # base_url: "https://api.alpaca.markets"  # Live trading
  
  api_config:
    rate_limit: 200  # requests per minute
    timeout: 30
    retry_attempts: 3
  
  trading_config:
    min_order_size: 1
    max_order_size: 10000
    supported_order_types:
      - "market"
      - "limit"
      - "stop"
      - "stop_limit"
      - "trailing_stop"
    
    # Commission-free trading
    fees:
      stock_commission: 0.0
      options_per_contract: 0.65
  
  # Supported markets
  supported_markets:
    - "NASDAQ"
    - "NYSE"
    - "AMEX"
  
  # Trading hours
  trading_hours:
    market_open: "09:30"
    market_close: "16:00"
    timezone: "America/New_York"
    extended_hours: true

# =============================================================================
# FOREX BROKERS
# =============================================================================

# OANDA Configuration
oanda:
  name: "OANDA"
  type: "forex"
  adapter_class: "adapters.forex.oanda.OandaAdapter"
  base_url: "https://api-fxpractice.oanda.com"  # Practice
  # base_url: "https://api-fxtrade.oanda.com"  # Live
  
  api_config:
    rate_limit: 120  # requests per minute
    timeout: 30
    retry_attempts: 3
  
  trading_config:
    min_order_size: 1  # 1 unit
    max_order_size: 10000000  # 10M units
    supported_order_types:
      - "MARKET"
      - "LIMIT"
      - "STOP"
      - "MARKET_IF_TOUCHED"
    
    # Spread-based pricing
    fees:
      spread_markup: 0.0001  # 1 pip markup
      financing_rate: 0.025   # 2.5% annual
  
  # Major currency pairs
  supported_instruments:
    - "EUR_USD"
    - "GBP_USD"
    - "USD_JPY"
    - "USD_CHF"
    - "AUD_USD"
    - "USD_CAD"
    - "NZD_USD"
  
  # Leverage settings
  leverage_config:
    default_leverage: 50
    max_leverage: 100
    margin_requirement: 0.02  # 2%

# FXCM Configuration
fxcm:
  name: "FXCM"
  type: "forex"
  adapter_class: "adapters.forex.fxcm.FXCMAdapter"
  base_url: "https://api-demo.fxcm.com"  # Demo
  # base_url: "https://api.fxcm.com"  # Live
  
  api_config:
    rate_limit: 300  # requests per minute
    timeout: 30
  
  trading_config:
    min_order_size: 1000  # 1K units
    max_order_size: 50000000  # 50M units
    supported_order_types:
      - "AtMarket"
      - "Entry"
      - "Limit"
      - "Stop"
    
    fees:
      commission_per_lot: 0.04  # $0.04 per 1K
      spread_markup: 0.0001
  
  supported_instruments:
    - "EUR/USD"
    - "GBP/USD"
    - "USD/JPY"
    - "USD/CHF"
    - "AUD/USD"

# =============================================================================
# CUSTOM BROKER TEMPLATE
# =============================================================================

custom_broker_template:
  name: "Custom Broker Name"
  type: "crypto|traditional|forex|custom"
  adapter_class: "adapters.custom.my_broker.MyBrokerAdapter"
  
  # Connection settings
  connection_config:
    base_url: "https://api.mybroker.com"
    websocket_url: "wss://ws.mybroker.com"
    timeout: 30
    retry_attempts: 3
  
  # API configuration
  api_config:
    rate_limit: 100  # requests per minute
    version: "v1"
    format: "json"
  
  # Trading configuration
  trading_config:
    min_order_size: 0.001
    max_order_size: 1000
    supported_order_types:
      - "MARKET"
      - "LIMIT"
      - "STOP"
    
    # Fee structure
    fees:
      maker: 0.001
      taker: 0.001
      withdrawal: 0.0005
  
  # Supported assets/instruments
  supported_assets:
    - "ASSET1"
    - "ASSET2"
  
  # Risk management
  risk_config:
    max_position_size: 0.1
    max_daily_trades: 100
    enable_stop_loss: true
    default_stop_loss: 0.02
  
  # Custom settings specific to this broker
  custom_config:
    special_feature_1: true
    special_parameter: "value"
    custom_endpoints:
      orders: "/v1/orders"
      positions: "/v1/positions"
      market_data: "/v1/market"

# =============================================================================
# BROKER SELECTION STRATEGIES
# =============================================================================

# Smart routing configuration
smart_routing:
  # Criteria for broker selection
  selection_criteria:
    - "lowest_fees"
    - "best_execution"
    - "fastest_execution"
    - "highest_liquidity"
  
  # Broker preferences by asset type
  asset_preferences:
    crypto:
      primary: "binance"
      secondary: "coinbase_pro"
    
    stocks:
      primary: "alpaca"
      secondary: "interactive_brokers"
    
    forex:
      primary: "oanda"
      secondary: "fxcm"
  
  # Failover configuration
  failover:
    enable: true
    max_retries: 3
    retry_delay: 5  # seconds
    fallback_broker: "interactive_brokers"

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================

# Development environment overrides
development_overrides:
  # Use demo/paper trading accounts
  force_demo_mode: true
  
  # Reduced rate limits for testing
  rate_limit_multiplier: 0.1
  
  # Enhanced logging
  debug_mode: true
  log_all_requests: true

# Production environment overrides
production_overrides:
  # Use live trading accounts
  force_demo_mode: false
  
  # Enhanced security
  require_2fa: true
  encrypt_all_communications: true
  
  # Performance optimizations
  connection_pooling: true
  request_compression: true