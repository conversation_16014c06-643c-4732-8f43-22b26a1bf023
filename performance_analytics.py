#!/usr/bin/env python3
"""
Noryon Performance Analytics System
Comprehensive performance analysis and reporting system

This module provides:
- Real-time performance tracking and analysis
- Advanced performance metrics calculation
- Risk-adjusted performance measures
- Attribution analysis (model, sector, time-based)
- Benchmark comparison and tracking error analysis
- Performance visualization and reporting
- Integration with backtesting and risk management systems
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import yaml
import warnings
from enum import Enum
from scipy import stats
import threading
import time
from concurrent.futures import ThreadPoolExecutor
warnings.filterwarnings('ignore')

# Import system components
try:
    from backtesting_framework import Trade, PerformanceMetrics
    from risk_management_system import Position, RiskMetrics, RiskMonitor
    from continuous_learning_pipeline import MarketData
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"System components not available: {e}")
    COMPONENTS_AVAILABLE = False

# Define missing classes if not available
if not COMPONENTS_AVAILABLE:
    @dataclass
    class Trade:
        """Trade execution record"""
        symbol: str
        action: str  # 'buy' or 'sell'
        quantity: float
        price: float
        timestamp: datetime = field(default_factory=datetime.now)
        model_name: str = "unknown"
        confidence: float = 0.0
        trade_id: str = field(default_factory=lambda: f"trade_{int(time.time())}")

    @dataclass
    class RiskMetrics:
        """Risk metrics container"""
        portfolio_value: float = 0.0
        total_exposure: float = 0.0
        net_exposure: float = 0.0
        gross_exposure: float = 0.0
        leverage: float = 0.0
        var_95: float = 0.0
        var_99: float = 0.0
        expected_shortfall_95: float = 0.0
        expected_shortfall_99: float = 0.0
        max_drawdown: float = 0.0
        current_drawdown: float = 0.0
        volatility: float = 0.0
        sharpe_ratio: float = 0.0
        beta: float = 0.0
        tracking_error: float = 0.0
        information_ratio: float = 0.0
        concentration_risk: float = 0.0
        liquidity_risk: float = 0.0
        model_risk: float = 0.0

    @dataclass
    class Position:
        """Position container"""
        symbol: str
        quantity: float
        entry_price: float
        current_price: float
        market_value: float
        unrealized_pnl: float
        realized_pnl: float
        timestamp: datetime = field(default_factory=datetime.now)
        model_name: str = "unknown"
        confidence: float = 0.0
        duration: timedelta = field(default_factory=lambda: timedelta(0))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformancePeriod(Enum):
    """Performance analysis periods"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    INCEPTION = "inception"

class AttributionType(Enum):
    """Attribution analysis types"""
    MODEL = "model"
    SECTOR = "sector"
    ASSET_CLASS = "asset_class"
    TIME_PERIOD = "time_period"
    STRATEGY = "strategy"

@dataclass
class PerformanceSnapshot:
    """Performance snapshot at a point in time"""
    timestamp: datetime
    portfolio_value: float
    cash_balance: float
    total_return: float
    period_return: float
    benchmark_return: float
    alpha: float
    beta: float
    sharpe_ratio: float
    sortino_ratio: float
    information_ratio: float
    tracking_error: float
    max_drawdown: float
    current_drawdown: float
    volatility: float
    var_95: float
    var_99: float
    win_rate: float
    profit_factor: float
    average_win: float
    average_loss: float
    total_trades: int
    winning_trades: int
    losing_trades: int

@dataclass
class ModelPerformance:
    """Performance metrics for individual models"""
    model_name: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    win_rate: float
    profit_factor: float
    average_trade_pnl: float
    best_trade: float
    worst_trade: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    volatility: float
    confidence_accuracy: float
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class AttributionResult:
    """Attribution analysis result"""
    attribution_type: AttributionType
    period: PerformancePeriod
    total_return: float
    benchmark_return: float
    active_return: float
    attribution_breakdown: Dict[str, float]
    top_contributors: List[Tuple[str, float]]
    bottom_contributors: List[Tuple[str, float]]
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class PerformanceReport:
    """Comprehensive performance report"""
    report_id: str
    start_date: datetime
    end_date: datetime
    period: PerformancePeriod
    summary_metrics: PerformanceSnapshot
    model_performance: Dict[str, ModelPerformance]
    attribution_analysis: List[AttributionResult]
    risk_metrics: Optional[RiskMetrics]
    benchmark_comparison: Dict[str, Any]
    trade_analysis: Dict[str, Any]
    recommendations: List[str]
    generated_at: datetime = field(default_factory=datetime.now)

class PerformanceCalculator:
    """Calculate various performance metrics"""
    
    @staticmethod
    def calculate_returns(prices: List[float]) -> List[float]:
        """Calculate returns from price series"""
        if len(prices) < 2:
            return []
        
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:
                returns.append((prices[i] - prices[i-1]) / prices[i-1])
            else:
                returns.append(0.0)
        return returns
    
    @staticmethod
    def calculate_cumulative_return(returns: List[float]) -> float:
        """Calculate cumulative return from return series"""
        if not returns:
            return 0.0
        
        cumulative = 1.0
        for ret in returns:
            cumulative *= (1 + ret)
        return cumulative - 1.0
    
    @staticmethod
    def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        if not returns or len(returns) < 2:
            return 0.0
        
        excess_returns = [r - risk_free_rate/252 for r in returns]  # Daily risk-free rate
        mean_excess = np.mean(excess_returns)
        std_excess = np.std(excess_returns)
        
        if std_excess == 0:
            return 0.0
        
        return (mean_excess / std_excess) * np.sqrt(252)  # Annualized
    
    @staticmethod
    def calculate_sortino_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio (downside deviation)"""
        if not returns or len(returns) < 2:
            return 0.0
        
        excess_returns = [r - risk_free_rate/252 for r in returns]
        mean_excess = np.mean(excess_returns)
        
        # Calculate downside deviation
        downside_returns = [r for r in excess_returns if r < 0]
        if not downside_returns:
            return float('inf') if mean_excess > 0 else 0.0
        
        downside_deviation = np.sqrt(np.mean([r**2 for r in downside_returns]))
        
        if downside_deviation == 0:
            return 0.0
        
        return (mean_excess / downside_deviation) * np.sqrt(252)  # Annualized
    
    @staticmethod
    def calculate_max_drawdown(prices: List[float]) -> Tuple[float, int, int]:
        """Calculate maximum drawdown and its duration"""
        if len(prices) < 2:
            return 0.0, 0, 0
        
        peak = prices[0]
        max_dd = 0.0
        max_dd_start = 0
        max_dd_end = 0
        current_dd_start = 0
        
        for i, price in enumerate(prices):
            if price > peak:
                peak = price
                current_dd_start = i
            else:
                drawdown = (peak - price) / peak
                if drawdown > max_dd:
                    max_dd = drawdown
                    max_dd_start = current_dd_start
                    max_dd_end = i
        
        return max_dd, max_dd_start, max_dd_end
    
    @staticmethod
    def calculate_beta(returns: List[float], benchmark_returns: List[float]) -> float:
        """Calculate beta relative to benchmark"""
        if len(returns) != len(benchmark_returns) or len(returns) < 2:
            return 0.0
        
        covariance = np.cov(returns, benchmark_returns)[0][1]
        benchmark_variance = np.var(benchmark_returns)
        
        if benchmark_variance == 0:
            return 0.0
        
        return covariance / benchmark_variance
    
    @staticmethod
    def calculate_alpha(returns: List[float], benchmark_returns: List[float], 
                      risk_free_rate: float = 0.02) -> float:
        """Calculate alpha (Jensen's alpha)"""
        if len(returns) != len(benchmark_returns) or len(returns) < 2:
            return 0.0
        
        beta = PerformanceCalculator.calculate_beta(returns, benchmark_returns)
        
        portfolio_return = np.mean(returns) * 252  # Annualized
        benchmark_return = np.mean(benchmark_returns) * 252  # Annualized
        
        expected_return = risk_free_rate + beta * (benchmark_return - risk_free_rate)
        
        return portfolio_return - expected_return
    
    @staticmethod
    def calculate_information_ratio(returns: List[float], benchmark_returns: List[float]) -> float:
        """Calculate information ratio"""
        if len(returns) != len(benchmark_returns) or len(returns) < 2:
            return 0.0
        
        active_returns = [r - b for r, b in zip(returns, benchmark_returns)]
        mean_active = np.mean(active_returns)
        tracking_error = np.std(active_returns)
        
        if tracking_error == 0:
            return 0.0
        
        return (mean_active / tracking_error) * np.sqrt(252)  # Annualized
    
    @staticmethod
    def calculate_tracking_error(returns: List[float], benchmark_returns: List[float]) -> float:
        """Calculate tracking error"""
        if len(returns) != len(benchmark_returns) or len(returns) < 2:
            return 0.0
        
        active_returns = [r - b for r, b in zip(returns, benchmark_returns)]
        return np.std(active_returns) * np.sqrt(252)  # Annualized

class TradeAnalyzer:
    """Analyze individual trades and trading patterns"""
    
    def __init__(self):
        self.trades = []
        self.trade_history = defaultdict(list)
    
    def add_trade(self, trade: Trade, exit_price: Optional[float] = None, 
                  exit_timestamp: Optional[datetime] = None):
        """Add completed trade for analysis"""
        trade_data = {
            'trade': trade,
            'exit_price': exit_price or trade.price,
            'exit_timestamp': exit_timestamp or trade.timestamp,
            'pnl': 0.0,
            'return_pct': 0.0,
            'duration': timedelta(0)
        }
        
        # Calculate P&L and return
        if trade.action == 'buy' and exit_price:
            trade_data['pnl'] = (exit_price - trade.price) * trade.quantity
            trade_data['return_pct'] = (exit_price - trade.price) / trade.price
        elif trade.action == 'sell' and exit_price:
            trade_data['pnl'] = (trade.price - exit_price) * trade.quantity
            trade_data['return_pct'] = (trade.price - exit_price) / exit_price
        
        # Calculate duration
        if exit_timestamp:
            trade_data['duration'] = exit_timestamp - trade.timestamp
        
        self.trades.append(trade_data)
        self.trade_history[trade.model_name].append(trade_data)
    
    def analyze_trades(self, model_name: Optional[str] = None) -> Dict[str, Any]:
        """Analyze trades for a specific model or all trades"""
        trades_to_analyze = self.trade_history.get(model_name, []) if model_name else self.trades
        
        if not trades_to_analyze:
            return {'total_trades': 0}
        
        # Basic statistics
        total_trades = len(trades_to_analyze)
        winning_trades = [t for t in trades_to_analyze if t['pnl'] > 0]
        losing_trades = [t for t in trades_to_analyze if t['pnl'] < 0]
        
        total_pnl = sum(t['pnl'] for t in trades_to_analyze)
        total_wins = sum(t['pnl'] for t in winning_trades)
        total_losses = sum(abs(t['pnl']) for t in losing_trades)
        
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        # Average metrics
        avg_trade_pnl = total_pnl / total_trades if total_trades > 0 else 0
        avg_win = total_wins / len(winning_trades) if winning_trades else 0
        avg_loss = total_losses / len(losing_trades) if losing_trades else 0
        
        # Best and worst trades
        best_trade = max(t['pnl'] for t in trades_to_analyze) if trades_to_analyze else 0
        worst_trade = min(t['pnl'] for t in trades_to_analyze) if trades_to_analyze else 0
        
        # Duration analysis
        durations = [t['duration'].total_seconds() / 3600 for t in trades_to_analyze]  # Hours
        avg_duration = np.mean(durations) if durations else 0
        
        # Return analysis
        returns = [t['return_pct'] for t in trades_to_analyze]
        volatility = np.std(returns) if len(returns) > 1 else 0
        
        # Confidence analysis (if available)
        confidences = [t['trade'].confidence for t in trades_to_analyze if hasattr(t['trade'], 'confidence')]
        confidence_accuracy = 0.0
        if confidences:
            # Calculate correlation between confidence and actual performance
            actual_performance = [1 if t['pnl'] > 0 else 0 for t in trades_to_analyze]
            if len(confidences) == len(actual_performance) and len(confidences) > 1:
                correlation = np.corrcoef(confidences, actual_performance)[0, 1]
                confidence_accuracy = correlation if not np.isnan(correlation) else 0.0
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'profit_factor': profit_factor,
            'average_trade_pnl': avg_trade_pnl,
            'average_win': avg_win,
            'average_loss': avg_loss,
            'best_trade': best_trade,
            'worst_trade': worst_trade,
            'average_duration_hours': avg_duration,
            'volatility': volatility,
            'confidence_accuracy': confidence_accuracy
        }
    
    def get_model_rankings(self) -> List[Tuple[str, Dict[str, Any]]]:
        """Get model performance rankings"""
        model_performances = []
        
        for model_name in self.trade_history.keys():
            analysis = self.analyze_trades(model_name)
            if analysis['total_trades'] > 0:
                # Calculate composite score
                score = (
                    analysis['win_rate'] * 0.3 +
                    min(analysis['profit_factor'] / 2.0, 1.0) * 0.3 +
                    (analysis['total_pnl'] / abs(analysis['total_pnl']) if analysis['total_pnl'] != 0 else 0) * 0.2 +
                    analysis['confidence_accuracy'] * 0.2
                )
                analysis['composite_score'] = score
                model_performances.append((model_name, analysis))
        
        # Sort by composite score
        model_performances.sort(key=lambda x: x[1]['composite_score'], reverse=True)
        return model_performances

class AttributionAnalyzer:
    """Perform attribution analysis"""
    
    def __init__(self):
        self.trade_analyzer = TradeAnalyzer()
        self.benchmark_returns = deque(maxlen=1000)
    
    def add_benchmark_return(self, return_value: float):
        """Add benchmark return for comparison"""
        self.benchmark_returns.append(return_value)
    
    def analyze_model_attribution(self, trades: List[Trade], 
                                period: PerformancePeriod) -> AttributionResult:
        """Analyze performance attribution by model"""
        model_contributions = defaultdict(float)
        total_pnl = 0.0
        
        for trade in trades:
            # Simplified P&L calculation (would need actual exit prices)
            pnl = trade.quantity * trade.price * 0.01  # Assume 1% return
            model_contributions[trade.model_name] += pnl
            total_pnl += pnl
        
        # Normalize contributions
        if total_pnl != 0:
            attribution_breakdown = {
                model: contrib / total_pnl for model, contrib in model_contributions.items()
            }
        else:
            attribution_breakdown = {model: 0.0 for model in model_contributions.keys()}
        
        # Sort contributors
        sorted_contributors = sorted(attribution_breakdown.items(), key=lambda x: x[1], reverse=True)
        top_contributors = sorted_contributors[:5]
        bottom_contributors = sorted_contributors[-5:]
        
        return AttributionResult(
            attribution_type=AttributionType.MODEL,
            period=period,
            total_return=total_pnl,
            benchmark_return=sum(self.benchmark_returns) if self.benchmark_returns else 0.0,
            active_return=total_pnl - (sum(self.benchmark_returns) if self.benchmark_returns else 0.0),
            attribution_breakdown=attribution_breakdown,
            top_contributors=top_contributors,
            bottom_contributors=bottom_contributors
        )
    
    def analyze_sector_attribution(self, trades: List[Trade], 
                                 period: PerformancePeriod) -> AttributionResult:
        """Analyze performance attribution by sector"""
        # Simplified sector mapping (would need actual sector data)
        sector_map = {
            'BTC': 'Crypto', 'ETH': 'Crypto', 'ADA': 'Crypto',
            'AAPL': 'Technology', 'MSFT': 'Technology', 'GOOGL': 'Technology',
            'JPM': 'Financial', 'BAC': 'Financial', 'GS': 'Financial',
            'XOM': 'Energy', 'CVX': 'Energy', 'COP': 'Energy'
        }
        
        sector_contributions = defaultdict(float)
        total_pnl = 0.0
        
        for trade in trades:
            # Extract base symbol
            base_symbol = trade.symbol.split('/')[0] if '/' in trade.symbol else trade.symbol
            sector = sector_map.get(base_symbol, 'Other')
            
            # Simplified P&L calculation
            pnl = trade.quantity * trade.price * 0.01
            sector_contributions[sector] += pnl
            total_pnl += pnl
        
        # Normalize contributions
        if total_pnl != 0:
            attribution_breakdown = {
                sector: contrib / total_pnl for sector, contrib in sector_contributions.items()
            }
        else:
            attribution_breakdown = {sector: 0.0 for sector in sector_contributions.keys()}
        
        # Sort contributors
        sorted_contributors = sorted(attribution_breakdown.items(), key=lambda x: x[1], reverse=True)
        top_contributors = sorted_contributors[:5]
        bottom_contributors = sorted_contributors[-5:]
        
        return AttributionResult(
            attribution_type=AttributionType.SECTOR,
            period=period,
            total_return=total_pnl,
            benchmark_return=sum(self.benchmark_returns) if self.benchmark_returns else 0.0,
            active_return=total_pnl - (sum(self.benchmark_returns) if self.benchmark_returns else 0.0),
            attribution_breakdown=attribution_breakdown,
            top_contributors=top_contributors,
            bottom_contributors=bottom_contributors
        )

class PerformanceAnalytics:
    """Main performance analytics system"""
    
    def __init__(self, benchmark_symbol: str = 'SPY'):
        self.benchmark_symbol = benchmark_symbol
        self.performance_history = deque(maxlen=10000)
        self.trade_analyzer = TradeAnalyzer()
        self.attribution_analyzer = AttributionAnalyzer()
        self.calculator = PerformanceCalculator()
        
        # Performance tracking
        self.portfolio_values = deque(maxlen=10000)
        self.returns = deque(maxlen=10000)
        self.benchmark_returns = deque(maxlen=10000)
        
        # Model performance tracking
        self.model_performances = {}
        
        # Threading
        self.lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def update_portfolio_value(self, portfolio_value: float, benchmark_value: float = None):
        """Update portfolio value and calculate returns"""
        with self.lock:
            self.portfolio_values.append(portfolio_value)
            
            # Calculate returns
            if len(self.portfolio_values) > 1:
                prev_value = self.portfolio_values[-2]
                if prev_value > 0:
                    portfolio_return = (portfolio_value - prev_value) / prev_value
                    self.returns.append(portfolio_return)
            
            # Update benchmark if provided
            if benchmark_value is not None and len(self.portfolio_values) > 1:
                # Assume previous benchmark value for return calculation
                benchmark_return = 0.001  # Simplified
                self.benchmark_returns.append(benchmark_return)
                self.attribution_analyzer.add_benchmark_return(benchmark_return)
    
    def add_trade(self, trade: Trade, exit_price: Optional[float] = None, 
                  exit_timestamp: Optional[datetime] = None):
        """Add trade for performance analysis"""
        self.trade_analyzer.add_trade(trade, exit_price, exit_timestamp)
        
        # Update model performance
        self.update_model_performance(trade.model_name)
    
    def update_model_performance(self, model_name: str):
        """Update performance metrics for a specific model"""
        analysis = self.trade_analyzer.analyze_trades(model_name)
        
        if analysis['total_trades'] > 0:
            # Calculate additional metrics
            model_trades = self.trade_analyzer.trade_history[model_name]
            returns = [t['return_pct'] for t in model_trades]
            
            sharpe_ratio = self.calculator.calculate_sharpe_ratio(returns) if len(returns) > 1 else 0
            sortino_ratio = self.calculator.calculate_sortino_ratio(returns) if len(returns) > 1 else 0
            
            # Calculate drawdown
            cumulative_pnl = []
            running_pnl = 0
            for trade in model_trades:
                running_pnl += trade['pnl']
                cumulative_pnl.append(running_pnl)
            
            max_drawdown, _, _ = self.calculator.calculate_max_drawdown(cumulative_pnl) if cumulative_pnl else (0, 0, 0)
            
            self.model_performances[model_name] = ModelPerformance(
                model_name=model_name,
                total_trades=analysis['total_trades'],
                winning_trades=analysis['winning_trades'],
                losing_trades=analysis['losing_trades'],
                total_pnl=analysis['total_pnl'],
                win_rate=analysis['win_rate'],
                profit_factor=analysis['profit_factor'],
                average_trade_pnl=analysis['average_trade_pnl'],
                best_trade=analysis['best_trade'],
                worst_trade=analysis['worst_trade'],
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                volatility=analysis['volatility'],
                confidence_accuracy=analysis['confidence_accuracy']
            )
    
    def calculate_current_performance(self) -> PerformanceSnapshot:
        """Calculate current performance snapshot"""
        if len(self.portfolio_values) < 2:
            return PerformanceSnapshot(
                timestamp=datetime.now(),
                portfolio_value=self.portfolio_values[-1] if self.portfolio_values else 0,
                cash_balance=0,
                total_return=0,
                period_return=0,
                benchmark_return=0,
                alpha=0,
                beta=0,
                sharpe_ratio=0,
                sortino_ratio=0,
                information_ratio=0,
                tracking_error=0,
                max_drawdown=0,
                current_drawdown=0,
                volatility=0,
                var_95=0,
                var_99=0,
                win_rate=0,
                profit_factor=0,
                average_win=0,
                average_loss=0,
                total_trades=0,
                winning_trades=0,
                losing_trades=0
            )
        
        # Calculate returns and metrics
        returns_list = list(self.returns)
        benchmark_returns_list = list(self.benchmark_returns)
        portfolio_values_list = list(self.portfolio_values)
        
        # Basic metrics
        current_value = portfolio_values_list[-1]
        initial_value = portfolio_values_list[0]
        total_return = (current_value - initial_value) / initial_value if initial_value > 0 else 0
        
        # Period return (last 30 days or available)
        period_start_idx = max(0, len(returns_list) - 30)
        period_returns = returns_list[period_start_idx:]
        period_return = self.calculator.calculate_cumulative_return(period_returns)
        
        # Risk-adjusted metrics
        sharpe_ratio = self.calculator.calculate_sharpe_ratio(returns_list)
        sortino_ratio = self.calculator.calculate_sortino_ratio(returns_list)
        
        # Benchmark comparison
        benchmark_return = self.calculator.calculate_cumulative_return(benchmark_returns_list)
        beta = self.calculator.calculate_beta(returns_list, benchmark_returns_list) if benchmark_returns_list else 0
        alpha = self.calculator.calculate_alpha(returns_list, benchmark_returns_list) if benchmark_returns_list else 0
        information_ratio = self.calculator.calculate_information_ratio(returns_list, benchmark_returns_list) if benchmark_returns_list else 0
        tracking_error = self.calculator.calculate_tracking_error(returns_list, benchmark_returns_list) if benchmark_returns_list else 0
        
        # Drawdown
        max_drawdown, _, _ = self.calculator.calculate_max_drawdown(portfolio_values_list)
        peak_value = max(portfolio_values_list)
        current_drawdown = (peak_value - current_value) / peak_value if peak_value > 0 else 0
        
        # Volatility
        volatility = np.std(returns_list) * np.sqrt(252) if len(returns_list) > 1 else 0
        
        # VaR (simplified)
        var_95 = abs(np.percentile(returns_list, 5)) if returns_list else 0
        var_99 = abs(np.percentile(returns_list, 1)) if returns_list else 0
        
        # Trade statistics
        all_trades_analysis = self.trade_analyzer.analyze_trades()
        
        return PerformanceSnapshot(
            timestamp=datetime.now(),
            portfolio_value=current_value,
            cash_balance=0,  # Would need from position manager
            total_return=total_return,
            period_return=period_return,
            benchmark_return=benchmark_return,
            alpha=alpha,
            beta=beta,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            information_ratio=information_ratio,
            tracking_error=tracking_error,
            max_drawdown=max_drawdown,
            current_drawdown=current_drawdown,
            volatility=volatility,
            var_95=var_95,
            var_99=var_99,
            win_rate=all_trades_analysis.get('win_rate', 0),
            profit_factor=all_trades_analysis.get('profit_factor', 0),
            average_win=all_trades_analysis.get('average_win', 0),
            average_loss=all_trades_analysis.get('average_loss', 0),
            total_trades=all_trades_analysis.get('total_trades', 0),
            winning_trades=all_trades_analysis.get('winning_trades', 0),
            losing_trades=all_trades_analysis.get('losing_trades', 0)
        )
    
    def generate_performance_report(self, period: PerformancePeriod = PerformancePeriod.MONTHLY) -> PerformanceReport:
        """Generate comprehensive performance report"""
        report_id = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Calculate date range based on period
        end_date = datetime.now()
        if period == PerformancePeriod.DAILY:
            start_date = end_date - timedelta(days=1)
        elif period == PerformancePeriod.WEEKLY:
            start_date = end_date - timedelta(weeks=1)
        elif period == PerformancePeriod.MONTHLY:
            start_date = end_date - timedelta(days=30)
        elif period == PerformancePeriod.QUARTERLY:
            start_date = end_date - timedelta(days=90)
        elif period == PerformancePeriod.YEARLY:
            start_date = end_date - timedelta(days=365)
        else:  # INCEPTION
            start_date = datetime.now() - timedelta(days=len(self.portfolio_values))
        
        # Get current performance snapshot
        summary_metrics = self.calculate_current_performance()
        
        # Model performance analysis
        model_performance = dict(self.model_performances)
        
        # Attribution analysis
        recent_trades = [t['trade'] for t in self.trade_analyzer.trades 
                        if t['trade'].timestamp >= start_date]
        
        attribution_analysis = []
        if recent_trades:
            model_attribution = self.attribution_analyzer.analyze_model_attribution(recent_trades, period)
            sector_attribution = self.attribution_analyzer.analyze_sector_attribution(recent_trades, period)
            attribution_analysis = [model_attribution, sector_attribution]
        
        # Benchmark comparison
        benchmark_comparison = {
            'benchmark_symbol': self.benchmark_symbol,
            'portfolio_return': summary_metrics.total_return,
            'benchmark_return': summary_metrics.benchmark_return,
            'active_return': summary_metrics.total_return - summary_metrics.benchmark_return,
            'tracking_error': summary_metrics.tracking_error,
            'information_ratio': summary_metrics.information_ratio
        }
        
        # Trade analysis
        trade_analysis = self.trade_analyzer.analyze_trades()
        trade_analysis['model_rankings'] = self.trade_analyzer.get_model_rankings()
        
        # Generate recommendations
        recommendations = self._generate_recommendations(summary_metrics, model_performance)
        
        return PerformanceReport(
            report_id=report_id,
            start_date=start_date,
            end_date=end_date,
            period=period,
            summary_metrics=summary_metrics,
            model_performance=model_performance,
            attribution_analysis=attribution_analysis,
            risk_metrics=None,  # Would integrate with risk management system
            benchmark_comparison=benchmark_comparison,
            trade_analysis=trade_analysis,
            recommendations=recommendations
        )
    
    def _generate_recommendations(self, performance: PerformanceSnapshot, 
                                model_performance: Dict[str, ModelPerformance]) -> List[str]:
        """Generate performance-based recommendations"""
        recommendations = []
        
        # Performance-based recommendations
        if performance.sharpe_ratio < 0.5:
            recommendations.append("Consider reducing position sizes or improving risk management - low Sharpe ratio")
        
        if performance.max_drawdown > 0.15:
            recommendations.append("Implement stricter drawdown controls - maximum drawdown exceeds 15%")
        
        if performance.win_rate < 0.4:
            recommendations.append("Review trade selection criteria - win rate below 40%")
        
        # Model-based recommendations
        if model_performance:
            best_models = sorted(model_performance.items(), 
                               key=lambda x: x[1].sharpe_ratio, reverse=True)[:3]
            worst_models = sorted(model_performance.items(), 
                                key=lambda x: x[1].sharpe_ratio)[:2]
            
            if best_models:
                best_model_names = [m[0] for m in best_models]
                recommendations.append(f"Consider increasing allocation to top-performing models: {', '.join(best_model_names)}")
            
            if worst_models and worst_models[0][1].sharpe_ratio < 0:
                worst_model_names = [m[0] for m in worst_models if m[1].sharpe_ratio < 0]
                if worst_model_names:
                    recommendations.append(f"Consider reducing or pausing underperforming models: {', '.join(worst_model_names)}")
        
        # Risk-based recommendations
        if performance.volatility > 0.3:
            recommendations.append("High portfolio volatility detected - consider diversification or position sizing adjustments")
        
        if performance.beta > 1.5:
            recommendations.append("High beta detected - portfolio is more volatile than benchmark")
        
        return recommendations
    
    def get_performance_dashboard(self) -> Dict[str, Any]:
        """Get real-time performance dashboard data"""
        current_performance = self.calculate_current_performance()
        
        # Model rankings
        model_rankings = self.trade_analyzer.get_model_rankings()
        
        # Recent performance trend
        recent_returns = list(self.returns)[-30:] if len(self.returns) >= 30 else list(self.returns)
        recent_values = list(self.portfolio_values)[-30:] if len(self.portfolio_values) >= 30 else list(self.portfolio_values)
        
        return {
            'current_performance': {
                'portfolio_value': current_performance.portfolio_value,
                'total_return': current_performance.total_return,
                'period_return': current_performance.period_return,
                'sharpe_ratio': current_performance.sharpe_ratio,
                'max_drawdown': current_performance.max_drawdown,
                'win_rate': current_performance.win_rate,
                'total_trades': current_performance.total_trades
            },
            'benchmark_comparison': {
                'portfolio_return': current_performance.total_return,
                'benchmark_return': current_performance.benchmark_return,
                'alpha': current_performance.alpha,
                'beta': current_performance.beta,
                'tracking_error': current_performance.tracking_error
            },
            'model_rankings': model_rankings[:10],  # Top 10 models
            'recent_performance': {
                'returns': recent_returns,
                'values': recent_values,
                'volatility': np.std(recent_returns) if recent_returns else 0
            },
            'timestamp': datetime.now().isoformat()
        }

# Example usage and testing
async def simulate_performance_analytics():
    """Simulate performance analytics with sample data"""
    logger.info("Starting performance analytics simulation")
    
    # Create performance analytics system
    analytics = PerformanceAnalytics(benchmark_symbol='SPY')
    
    # Simulate portfolio performance
    initial_value = 100000
    current_value = initial_value
    
    symbols = ['BTC/USD', 'ETH/USD', 'AAPL', 'TSLA', 'GOOGL']
    models = ['noryon-gemma-3-finance', 'noryon-phi-4-finance', 'ensemble']
    
    for i in range(100):
        # Simulate market movement
        market_return = np.random.normal(0.001, 0.02)  # 0.1% daily return, 2% volatility
        current_value *= (1 + market_return)
        
        # Update portfolio value
        benchmark_value = initial_value * (1 + np.random.normal(0.0008, 0.015))  # Benchmark performance
        analytics.update_portfolio_value(current_value, benchmark_value)
        
        # Simulate trades
        if i % 5 == 0:  # Trade every 5 periods
            symbol = np.random.choice(symbols)
            model = np.random.choice(models)
            action = np.random.choice(['buy', 'sell'])
            price = np.random.uniform(100, 1000)
            quantity = np.random.uniform(1, 10)
            
            trade = Trade(
                timestamp=datetime.now() - timedelta(days=100-i),
                symbol=symbol,
                action=action,
                quantity=quantity,
                price=price,
                model_name=model,
                confidence=np.random.uniform(0.5, 0.95)
            )
            
            # Simulate trade exit
            exit_price = price * (1 + np.random.normal(0, 0.05))  # 5% price movement
            exit_timestamp = trade.timestamp + timedelta(hours=np.random.uniform(1, 24))
            
            analytics.add_trade(trade, exit_price, exit_timestamp)
        
        # Print dashboard every 20 periods
        if i % 20 == 0:
            dashboard = analytics.get_performance_dashboard()
            print(f"\n=== Performance Dashboard (Period {i+1}) ===")
            perf = dashboard['current_performance']
            print(f"Portfolio Value: ${perf['portfolio_value']:,.2f}")
            print(f"Total Return: {perf['total_return']:.2%}")
            print(f"Sharpe Ratio: {perf['sharpe_ratio']:.2f}")
            print(f"Max Drawdown: {perf['max_drawdown']:.2%}")
            print(f"Win Rate: {perf['win_rate']:.2%}")
            print(f"Total Trades: {perf['total_trades']}")
            
            if dashboard['model_rankings']:
                print("\nTop Models:")
                for j, (model_name, model_perf) in enumerate(dashboard['model_rankings'][:3]):
                    print(f"  {j+1}. {model_name}: {model_perf['win_rate']:.1%} win rate, {model_perf['total_pnl']:.2f} P&L")
    
    # Generate final report
    report = analytics.generate_performance_report(PerformancePeriod.INCEPTION)
    
    print("\n=== Final Performance Report ===")
    print(f"Report ID: {report.report_id}")
    print(f"Period: {report.start_date.date()} to {report.end_date.date()}")
    print(f"Total Return: {report.summary_metrics.total_return:.2%}")
    print(f"Sharpe Ratio: {report.summary_metrics.sharpe_ratio:.2f}")
    print(f"Max Drawdown: {report.summary_metrics.max_drawdown:.2%}")
    print(f"Total Trades: {report.summary_metrics.total_trades}")
    
    print("\nRecommendations:")
    for rec in report.recommendations:
        print(f"  - {rec}")
    
    # Save report
    report_path = Path(f"performance_reports/{report.report_id}.json")
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w') as f:
        json.dump({
            'report_id': report.report_id,
            'period': report.period.value,
            'start_date': report.start_date.isoformat(),
            'end_date': report.end_date.isoformat(),
            'summary_metrics': {
                'portfolio_value': report.summary_metrics.portfolio_value,
                'total_return': report.summary_metrics.total_return,
                'sharpe_ratio': report.summary_metrics.sharpe_ratio,
                'max_drawdown': report.summary_metrics.max_drawdown,
                'win_rate': report.summary_metrics.win_rate,
                'total_trades': report.summary_metrics.total_trades
            },
            'recommendations': report.recommendations,
            'generated_at': report.generated_at.isoformat()
        }, indent=2)
    
    print(f"\nReport saved to: {report_path}")

if __name__ == "__main__":
    asyncio.run(simulate_performance_analytics())