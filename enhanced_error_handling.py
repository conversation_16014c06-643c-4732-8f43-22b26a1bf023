#!/usr/bin/env python3
"""
ENHANCED ERROR HANDLING FRAMEWORK
Extends existing System Recovery Manager with model-specific error handling,
graceful degradation, and structured error logging for the AI trading system
"""

import sqlite3
import time
import traceback
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum
import json
from pathlib import Path

# Import existing error handler
try:
    from error_handler import <PERSON><PERSON>r<PERSON><PERSON>ler as BaseErrorHandler
except ImportError:
    # Fallback if base error handler not available
    class BaseErrorHandler:
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            self.error_counts = {}
            self.recovery_strategies = {}

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    MODEL_TIMEOUT = "model_timeout"
    INVALID_RESPONSE = "invalid_response"
    CONFIDENCE_FAILURE = "confidence_failure"
    NETWORK_ERROR = "network_error"
    PARSING_ERROR = "parsing_error"
    VALIDATION_ERROR = "validation_error"
    SYSTEM_ERROR = "system_error"
    DATABASE_ERROR = "database_error"

@dataclass
class ErrorEvent:
    """Structured error event"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    model_name: Optional[str]
    symbol: Optional[str]
    error_message: str
    stack_trace: str
    context: Dict[str, Any]
    recovery_attempted: bool
    recovery_successful: bool
    timestamp: datetime
    resolution_time: Optional[float] = None

@dataclass
class RecoveryStrategy:
    """Recovery strategy definition"""
    name: str
    category: ErrorCategory
    handler_function: Callable
    max_retries: int
    retry_delay: float
    fallback_strategy: Optional[str] = None

class EnhancedErrorHandler(BaseErrorHandler):
    """
    Enhanced error handling system with model-specific recovery strategies
    Integrates with existing 15 databases and maintains 86.3s performance baseline
    """
    
    def __init__(self, db_path: str = "enhanced_error_handling.db"):
        super().__init__()
        self.db_path = db_path
        self.setup_database()
        
        # Error tracking
        self.error_events = []
        self.recovery_strategies = {}
        self.model_error_rates = {}
        self.system_health_status = "healthy"
        
        # Performance tracking
        self.performance_baseline = 86.3  # seconds
        self.current_performance = 86.3
        self.degradation_threshold = 1.2  # 20% degradation threshold
        
        # Initialize recovery strategies
        self._initialize_recovery_strategies()
        
        logger.info("🛡️ Enhanced Error Handling Framework initialized")
        logger.info(f"   📊 Performance baseline: {self.performance_baseline}s")
        logger.info(f"   🗃️ Database: {self.db_path}")
        logger.info(f"   🔧 Recovery strategies: {len(self.recovery_strategies)}")
    
    def setup_database(self):
        """Setup enhanced error tracking database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS error_events (
                id INTEGER PRIMARY KEY,
                error_id TEXT,
                category TEXT,
                severity TEXT,
                model_name TEXT,
                symbol TEXT,
                error_message TEXT,
                stack_trace TEXT,
                context TEXT,
                recovery_attempted BOOLEAN,
                recovery_successful BOOLEAN,
                resolution_time REAL,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_error_rates (
                id INTEGER PRIMARY KEY,
                model_name TEXT,
                error_category TEXT,
                error_count INTEGER,
                total_requests INTEGER,
                error_rate REAL,
                last_error DATETIME,
                last_updated DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_health (
                id INTEGER PRIMARY KEY,
                health_status TEXT,
                performance_current REAL,
                performance_baseline REAL,
                degradation_level REAL,
                active_errors INTEGER,
                recovery_actions INTEGER,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recovery_actions (
                id INTEGER PRIMARY KEY,
                error_id TEXT,
                strategy_name TEXT,
                action_taken TEXT,
                success BOOLEAN,
                execution_time REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("✅ Enhanced error handling database initialized")
    
    def _initialize_recovery_strategies(self):
        """Initialize model-specific recovery strategies"""
        
        # Model timeout recovery
        self.register_recovery_strategy(RecoveryStrategy(
            name="model_timeout_recovery",
            category=ErrorCategory.MODEL_TIMEOUT,
            handler_function=self._handle_model_timeout,
            max_retries=2,
            retry_delay=5.0,
            fallback_strategy="technical_analysis_fallback"
        ))
        
        # Invalid response recovery
        self.register_recovery_strategy(RecoveryStrategy(
            name="invalid_response_recovery",
            category=ErrorCategory.INVALID_RESPONSE,
            handler_function=self._handle_invalid_response,
            max_retries=1,
            retry_delay=2.0,
            fallback_strategy="ensemble_fallback"
        ))
        
        # Confidence failure recovery
        self.register_recovery_strategy(RecoveryStrategy(
            name="confidence_failure_recovery",
            category=ErrorCategory.CONFIDENCE_FAILURE,
            handler_function=self._handle_confidence_failure,
            max_retries=0,
            retry_delay=0.0,
            fallback_strategy="conservative_default"
        ))
        
        # Network error recovery
        self.register_recovery_strategy(RecoveryStrategy(
            name="network_error_recovery",
            category=ErrorCategory.NETWORK_ERROR,
            handler_function=self._handle_network_error,
            max_retries=3,
            retry_delay=10.0,
            fallback_strategy="cached_analysis"
        ))
        
        # Validation error recovery
        self.register_recovery_strategy(RecoveryStrategy(
            name="validation_error_recovery",
            category=ErrorCategory.VALIDATION_ERROR,
            handler_function=self._handle_validation_error,
            max_retries=1,
            retry_delay=1.0,
            fallback_strategy="sanitized_output"
        ))
        
        logger.info(f"✅ Initialized {len(self.recovery_strategies)} recovery strategies")
    
    def register_recovery_strategy(self, strategy: RecoveryStrategy):
        """Register a new recovery strategy"""
        self.recovery_strategies[strategy.category] = strategy
        logger.debug(f"Registered recovery strategy: {strategy.name}")
    
    def handle_enhanced_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced error handling with categorization and recovery
        Returns recovery result with fallback information
        """
        start_time = time.time()
        
        # Categorize error
        category = self._categorize_error(error, context)
        severity = self._assess_severity(error, category, context)
        
        # Create error event
        error_event = ErrorEvent(
            error_id=f"ERR_{int(time.time())}_{hash(str(error)) % 10000}",
            category=category,
            severity=severity,
            model_name=context.get('model_name'),
            symbol=context.get('symbol'),
            error_message=str(error),
            stack_trace=traceback.format_exc(),
            context=context,
            recovery_attempted=False,
            recovery_successful=False,
            timestamp=datetime.now()
        )
        
        # Log error event
        self._log_error_event(error_event)
        
        # Attempt recovery
        recovery_result = self._attempt_recovery(error_event)
        
        # Update error event with recovery results
        error_event.recovery_attempted = recovery_result['attempted']
        error_event.recovery_successful = recovery_result['successful']
        error_event.resolution_time = time.time() - start_time
        
        # Update error rates
        self._update_error_rates(error_event)
        
        # Update system health
        self._update_system_health()
        
        # Log final error event
        self._update_error_event(error_event)
        
        logger.info(f"🛡️ Error handled: {category.value} - Recovery: {recovery_result['successful']}")
        
        return {
            'error_id': error_event.error_id,
            'category': category.value,
            'severity': severity.value,
            'recovery_successful': recovery_result['successful'],
            'fallback_data': recovery_result.get('fallback_data'),
            'resolution_time': error_event.resolution_time
        }

    def _categorize_error(self, error: Exception, context: Dict[str, Any]) -> ErrorCategory:
        """Categorize error based on type and context"""
        error_type = type(error).__name__
        error_message = str(error).lower()

        if 'timeout' in error_message or error_type == 'TimeoutExpired':
            return ErrorCategory.MODEL_TIMEOUT
        elif 'network' in error_message or 'connection' in error_message:
            return ErrorCategory.NETWORK_ERROR
        elif 'json' in error_message or 'parse' in error_message:
            return ErrorCategory.PARSING_ERROR
        elif 'validation' in error_message or context.get('validation_failed'):
            return ErrorCategory.VALIDATION_ERROR
        elif 'confidence' in error_message or context.get('low_confidence'):
            return ErrorCategory.CONFIDENCE_FAILURE
        elif 'database' in error_message or 'sqlite' in error_message:
            return ErrorCategory.DATABASE_ERROR
        elif context.get('invalid_response'):
            return ErrorCategory.INVALID_RESPONSE
        else:
            return ErrorCategory.SYSTEM_ERROR

    def _assess_severity(self, error: Exception, category: ErrorCategory, context: Dict[str, Any]) -> ErrorSeverity:
        """Assess error severity based on impact"""

        # Critical errors that could stop trading
        if category in [ErrorCategory.SYSTEM_ERROR, ErrorCategory.DATABASE_ERROR]:
            return ErrorSeverity.CRITICAL

        # High severity for multiple model failures
        if context.get('multiple_failures', False):
            return ErrorSeverity.HIGH

        # Medium severity for single model issues
        if category in [ErrorCategory.MODEL_TIMEOUT, ErrorCategory.INVALID_RESPONSE]:
            return ErrorSeverity.MEDIUM

        # Low severity for recoverable issues
        return ErrorSeverity.LOW

    def _attempt_recovery(self, error_event: ErrorEvent) -> Dict[str, Any]:
        """Attempt recovery using registered strategies"""

        if error_event.category not in self.recovery_strategies:
            return {
                'attempted': False,
                'successful': False,
                'reason': 'No recovery strategy available'
            }

        strategy = self.recovery_strategies[error_event.category]

        for attempt in range(strategy.max_retries + 1):
            try:
                logger.info(f"🔧 Recovery attempt {attempt + 1}/{strategy.max_retries + 1} for {error_event.category.value}")

                # Execute recovery strategy
                recovery_result = strategy.handler_function(error_event, attempt)

                if recovery_result['successful']:
                    self._log_recovery_action(error_event.error_id, strategy.name,
                                            recovery_result['action'], True)
                    return {
                        'attempted': True,
                        'successful': True,
                        'strategy': strategy.name,
                        'attempt': attempt + 1,
                        'fallback_data': recovery_result.get('data')
                    }

                # Wait before retry
                if attempt < strategy.max_retries:
                    time.sleep(strategy.retry_delay)

            except Exception as recovery_error:
                logger.error(f"Recovery strategy failed: {recovery_error}")
                self._log_recovery_action(error_event.error_id, strategy.name,
                                        f"Failed: {recovery_error}", False)

        # All retries failed, try fallback strategy
        if strategy.fallback_strategy:
            fallback_result = self._execute_fallback(error_event, strategy.fallback_strategy)
            return {
                'attempted': True,
                'successful': fallback_result['successful'],
                'strategy': strategy.fallback_strategy,
                'fallback_data': fallback_result.get('data')
            }

        return {
            'attempted': True,
            'successful': False,
            'reason': 'All recovery attempts failed'
        }

    def _handle_model_timeout(self, error_event: ErrorEvent, attempt: int) -> Dict[str, Any]:
        """Handle model timeout errors"""

        # Try with shorter timeout or different model
        if attempt == 0:
            return {
                'successful': False,
                'action': 'Retry with shorter timeout',
                'reason': 'First attempt with reduced timeout'
            }
        else:
            # Switch to faster model or technical analysis only
            return {
                'successful': True,
                'action': 'Switched to technical analysis fallback',
                'data': {
                    'fallback_mode': 'technical_analysis_only',
                    'reason': 'Model timeout recovery'
                }
            }

    def _handle_invalid_response(self, error_event: ErrorEvent, attempt: int) -> Dict[str, Any]:
        """Handle invalid response errors"""

        # Try to parse response differently or use ensemble fallback
        return {
            'successful': True,
            'action': 'Using ensemble fallback for invalid response',
            'data': {
                'fallback_mode': 'ensemble_voting',
                'exclude_model': error_event.model_name,
                'reason': 'Invalid response recovery'
            }
        }

    def _handle_confidence_failure(self, error_event: ErrorEvent, attempt: int) -> Dict[str, Any]:
        """Handle low confidence errors"""

        # Use conservative default action
        return {
            'successful': True,
            'action': 'Applied conservative default due to low confidence',
            'data': {
                'fallback_mode': 'conservative_default',
                'action': 'HOLD',
                'confidence': 0.3,
                'reason': 'Low confidence recovery'
            }
        }

    def _handle_network_error(self, error_event: ErrorEvent, attempt: int) -> Dict[str, Any]:
        """Handle network connectivity errors"""

        if attempt < 2:
            return {
                'successful': False,
                'action': f'Network retry attempt {attempt + 1}',
                'reason': 'Retrying network connection'
            }
        else:
            # Use cached analysis
            return {
                'successful': True,
                'action': 'Using cached analysis due to network issues',
                'data': {
                    'fallback_mode': 'cached_analysis',
                    'reason': 'Network error recovery'
                }
            }

    def _handle_validation_error(self, error_event: ErrorEvent, attempt: int) -> Dict[str, Any]:
        """Handle validation errors"""

        # Try to sanitize and revalidate
        return {
            'successful': True,
            'action': 'Applied output sanitization',
            'data': {
                'fallback_mode': 'sanitized_output',
                'reason': 'Validation error recovery'
            }
        }

    def _execute_fallback(self, error_event: ErrorEvent, fallback_strategy: str) -> Dict[str, Any]:
        """Execute fallback strategy"""

        fallback_strategies = {
            'technical_analysis_fallback': self._technical_analysis_fallback,
            'ensemble_fallback': self._ensemble_fallback,
            'conservative_default': self._conservative_default_fallback,
            'cached_analysis': self._cached_analysis_fallback,
            'sanitized_output': self._sanitized_output_fallback
        }

        if fallback_strategy in fallback_strategies:
            try:
                return fallback_strategies[fallback_strategy](error_event)
            except Exception as e:
                logger.error(f"Fallback strategy {fallback_strategy} failed: {e}")
                return {'successful': False, 'reason': f'Fallback failed: {e}'}

        return {'successful': False, 'reason': 'Unknown fallback strategy'}

    def _technical_analysis_fallback(self, error_event: ErrorEvent) -> Dict[str, Any]:
        """Fallback to technical analysis only"""
        return {
            'successful': True,
            'data': {
                'mode': 'technical_analysis_only',
                'confidence': 0.6,
                'source': 'Professional TA Engine',
                'reason': 'AI model failure fallback'
            }
        }

    def _ensemble_fallback(self, error_event: ErrorEvent) -> Dict[str, Any]:
        """Fallback to ensemble voting without failed model"""
        return {
            'successful': True,
            'data': {
                'mode': 'ensemble_voting',
                'excluded_models': [error_event.model_name] if error_event.model_name else [],
                'min_models': 2,
                'reason': 'Model failure ensemble fallback'
            }
        }

    def _conservative_default_fallback(self, error_event: ErrorEvent) -> Dict[str, Any]:
        """Conservative default action fallback"""
        return {
            'successful': True,
            'data': {
                'action': 'HOLD',
                'confidence': 0.3,
                'reasoning': 'Conservative default due to system errors',
                'reason': 'Conservative fallback'
            }
        }

    def _cached_analysis_fallback(self, error_event: ErrorEvent) -> Dict[str, Any]:
        """Fallback to cached analysis"""
        return {
            'successful': True,
            'data': {
                'mode': 'cached_analysis',
                'cache_age_limit': 300,  # 5 minutes
                'reason': 'Network error fallback'
            }
        }

    def _sanitized_output_fallback(self, error_event: ErrorEvent) -> Dict[str, Any]:
        """Fallback with sanitized output"""
        return {
            'successful': True,
            'data': {
                'mode': 'sanitized_output',
                'action': 'HOLD',
                'confidence': 0.4,
                'reasoning': 'Sanitized output due to validation errors',
                'reason': 'Validation error fallback'
            }
        }

    def _log_error_event(self, error_event: ErrorEvent):
        """Log error event to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO error_events
            (error_id, category, severity, model_name, symbol, error_message,
             stack_trace, context, recovery_attempted, recovery_successful,
             resolution_time, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            error_event.error_id, error_event.category.value, error_event.severity.value,
            error_event.model_name, error_event.symbol, error_event.error_message,
            error_event.stack_trace, json.dumps(error_event.context),
            error_event.recovery_attempted, error_event.recovery_successful,
            error_event.resolution_time, error_event.timestamp
        ))

        conn.commit()
        conn.close()

    def _update_error_event(self, error_event: ErrorEvent):
        """Update error event with recovery results"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE error_events
            SET recovery_attempted = ?, recovery_successful = ?, resolution_time = ?
            WHERE error_id = ?
        ''', (
            error_event.recovery_attempted, error_event.recovery_successful,
            error_event.resolution_time, error_event.error_id
        ))

        conn.commit()
        conn.close()

    def _log_recovery_action(self, error_id: str, strategy_name: str,
                           action_taken: str, success: bool):
        """Log recovery action"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO recovery_actions
            (error_id, strategy_name, action_taken, success, execution_time, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            error_id, strategy_name, action_taken, success,
            time.time(), datetime.now()
        ))

        conn.commit()
        conn.close()

    def _update_error_rates(self, error_event: ErrorEvent):
        """Update model error rates"""
        if not error_event.model_name:
            return

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get current stats
        cursor.execute('''
            SELECT error_count, total_requests FROM model_error_rates
            WHERE model_name = ? AND error_category = ?
        ''', (error_event.model_name, error_event.category.value))

        result = cursor.fetchone()

        if result:
            error_count, total_requests = result
            error_count += 1
            total_requests += 1
            error_rate = error_count / total_requests

            cursor.execute('''
                UPDATE model_error_rates
                SET error_count = ?, total_requests = ?, error_rate = ?,
                    last_error = ?, last_updated = ?
                WHERE model_name = ? AND error_category = ?
            ''', (
                error_count, total_requests, error_rate,
                datetime.now(), datetime.now(),
                error_event.model_name, error_event.category.value
            ))
        else:
            cursor.execute('''
                INSERT INTO model_error_rates
                (model_name, error_category, error_count, total_requests,
                 error_rate, last_error, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                error_event.model_name, error_event.category.value,
                1, 1, 1.0, datetime.now(), datetime.now()
            ))

        conn.commit()
        conn.close()

    def _update_system_health(self):
        """Update overall system health status"""

        # Count recent errors (last hour)
        one_hour_ago = datetime.now() - timedelta(hours=1)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT COUNT(*) FROM error_events
            WHERE timestamp > ? AND severity IN ('high', 'critical')
        ''', (one_hour_ago,))

        critical_errors = cursor.fetchone()[0]

        cursor.execute('''
            SELECT COUNT(*) FROM recovery_actions
            WHERE timestamp > ? AND success = 1
        ''', (one_hour_ago,))

        successful_recoveries = cursor.fetchone()[0]

        # Determine health status
        if critical_errors > 5:
            self.system_health_status = "critical"
        elif critical_errors > 2:
            self.system_health_status = "degraded"
        elif critical_errors > 0:
            self.system_health_status = "warning"
        else:
            self.system_health_status = "healthy"

        # Calculate performance degradation
        degradation_level = self.current_performance / self.performance_baseline

        # Log system health
        cursor.execute('''
            INSERT INTO system_health
            (health_status, performance_current, performance_baseline,
             degradation_level, active_errors, recovery_actions, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            self.system_health_status, self.current_performance,
            self.performance_baseline, degradation_level,
            critical_errors, successful_recoveries, datetime.now()
        ))

        conn.commit()
        conn.close()

    def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """Get error statistics for specified time period"""

        since = datetime.now() - timedelta(hours=hours)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Total errors by category
        cursor.execute('''
            SELECT category, COUNT(*) FROM error_events
            WHERE timestamp > ? GROUP BY category
        ''', (since,))

        errors_by_category = dict(cursor.fetchall())

        # Recovery success rate
        cursor.execute('''
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN recovery_successful = 1 THEN 1 ELSE 0 END) as successful
            FROM error_events WHERE timestamp > ?
        ''', (since,))

        recovery_stats = cursor.fetchone()
        recovery_rate = recovery_stats[1] / recovery_stats[0] if recovery_stats[0] > 0 else 0

        # Model error rates
        cursor.execute('''
            SELECT model_name, AVG(error_rate) FROM model_error_rates
            WHERE last_updated > ? GROUP BY model_name
        ''', (since,))

        model_error_rates = dict(cursor.fetchall())

        conn.close()

        return {
            'time_period_hours': hours,
            'total_errors': sum(errors_by_category.values()),
            'errors_by_category': errors_by_category,
            'recovery_success_rate': recovery_rate,
            'model_error_rates': model_error_rates,
            'system_health': self.system_health_status,
            'performance_degradation': self.current_performance / self.performance_baseline
        }

    def update_performance_metrics(self, current_performance: float):
        """Update current performance metrics"""
        self.current_performance = current_performance

        # Check if performance degradation requires action
        degradation = current_performance / self.performance_baseline
        if degradation > self.degradation_threshold:
            logger.warning(f"⚠️ Performance degradation detected: {degradation:.2f}x baseline")

            # Trigger performance recovery if needed
            self._trigger_performance_recovery(degradation)

    def _trigger_performance_recovery(self, degradation_level: float):
        """Trigger performance recovery actions"""

        if degradation_level > 2.0:  # 100% degradation
            logger.critical("🚨 Critical performance degradation - enabling emergency mode")
            # Could trigger emergency fallback to technical analysis only
        elif degradation_level > 1.5:  # 50% degradation
            logger.warning("⚠️ Significant performance degradation - reducing model complexity")
            # Could reduce number of models in ensemble

        # Log performance recovery action
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO recovery_actions
            (error_id, strategy_name, action_taken, success, execution_time, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            f"PERF_{int(time.time())}", "performance_recovery",
            f"Degradation level: {degradation_level:.2f}", True,
            time.time(), datetime.now()
        ))

        conn.commit()
        conn.close()

    def get_system_health_report(self) -> Dict[str, Any]:
        """Get comprehensive system health report"""

        error_stats = self.get_error_statistics(24)

        return {
            'system_health_status': self.system_health_status,
            'performance_baseline': self.performance_baseline,
            'current_performance': self.current_performance,
            'degradation_level': self.current_performance / self.performance_baseline,
            'error_statistics': error_stats,
            'recovery_strategies_count': len(self.recovery_strategies),
            'database_path': self.db_path,
            'timestamp': datetime.now()
        }
