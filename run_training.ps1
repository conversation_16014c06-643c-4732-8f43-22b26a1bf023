# Set SSL certificate paths
$env:SSL_CERT_FILE = 'C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\certifi\cacert.pem'
$env:REQUESTS_CA_BUNDLE = 'C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\certifi\cacert.pem'
$env:CURL_CA_BUNDLE = 'C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\certifi\cacert.pem'

# Display confirmation message
Write-Host "Starting model training with SSL certificate fix..." -ForegroundColor Green

# Run the training script
python train_all_models.py --parallel --gpu-count 2

# Display completion message
Write-Host "Training completed." -ForegroundColor Green

# Keep the window open
Read-Host -Prompt "Press Enter to exit"