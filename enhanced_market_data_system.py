#!/usr/bin/env python3
"""
ENHANCED MARKET DATA SYSTEM
Advanced market data feeds with maximum reliability and alternative data sources
"""

import asyncio
import time
import logging
import json
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, AsyncGenerator
from dataclasses import dataclass, asdict
import sqlite3
from pathlib import Path
import yfinance as yf
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
import numpy as np
from collections import defaultdict, deque

@dataclass
class MarketDataPoint:
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None
    source: str = "unknown"
    quality_score: float = 1.0

@dataclass
class AlternativeDataPoint:
    symbol: str
    data_type: str  # NEWS, SOCIAL, ECONOMIC, EARNINGS, OPTIONS_FLOW
    timestamp: datetime
    value: Any
    sentiment_score: Optional[float] = None
    confidence: float = 1.0
    source: str = "unknown"

@dataclass
class DataSourceConfig:
    source_id: str
    source_type: str
    endpoint: str
    api_key: Optional[str] = None
    rate_limit: int = 100  # requests per minute
    reliability_score: float = 1.0
    enabled: bool = True

class EnhancedMarketDataSystem:
    """ENHANCED market data system with multiple sources and reliability features"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_sources: Dict[str, DataSourceConfig] = {}
        self.market_data_cache: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.alternative_data_cache: Dict[str, deque] = defaultdict(lambda: deque(maxlen=500))
        self.source_health: Dict[str, float] = {}
        self.active_subscriptions: Dict[str, bool] = {}
        
        # Data quality metrics
        self.data_quality_metrics = {
            'total_data_points': 0,
            'failed_requests': 0,
            'latency_avg': 0.0,
            'source_uptime': {},
            'data_freshness': {}
        }
        
        # Enhanced data directories
        self.data_dir = Path("enhanced_data")
        self.cache_dir = self.data_dir / "cache"
        self.logs_dir = self.data_dir / "logs"
        self.config_dir = self.data_dir / "config"
        
        self._setup_data_infrastructure()
        self._initialize_data_sources()
        
        print("📊 ENHANCED MARKET DATA SYSTEM INITIALIZED")
        print(f"   🔗 Data sources: {len(self.data_sources)}")
        print(f"   📈 Market data feeds: READY")
        print(f"   🔍 Alternative data: READY")
        print(f"   🛡️ Reliability monitoring: ACTIVE")
    
    def _setup_data_infrastructure(self):
        """Setup enhanced data infrastructure"""
        # Create directories
        for directory in [self.data_dir, self.cache_dir, self.logs_dir, self.config_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Setup enhanced data database
        self.data_db_path = self.data_dir / "enhanced_market_data.db"
        self._initialize_data_database()
    
    def _initialize_data_database(self):
        """Initialize enhanced data database"""
        conn = sqlite3.connect(str(self.data_db_path))
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                timestamp TIMESTAMP,
                price REAL,
                volume INTEGER,
                bid REAL,
                ask REAL,
                high REAL,
                low REAL,
                open REAL,
                source TEXT,
                quality_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alternative_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                data_type TEXT,
                timestamp TIMESTAMP,
                value TEXT,
                sentiment_score REAL,
                confidence REAL,
                source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_sources (
                source_id TEXT PRIMARY KEY,
                source_type TEXT,
                endpoint TEXT,
                api_key TEXT,
                rate_limit INTEGER,
                reliability_score REAL,
                enabled BOOLEAN,
                last_updated TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_quality_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_id TEXT,
                metric_type TEXT,
                metric_value REAL,
                timestamp TIMESTAMP
            )
        ''')
        
        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol, timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_alternative_data_symbol_type ON alternative_data(symbol, data_type)')
        
        conn.commit()
        conn.close()
    
    def _initialize_data_sources(self):
        """Initialize enhanced data sources"""
        data_sources_config = [
            # Primary Market Data Sources
            DataSourceConfig(
                source_id="yahoo_finance",
                source_type="MARKET_DATA",
                endpoint="https://query1.finance.yahoo.com/v8/finance/chart/",
                rate_limit=2000,
                reliability_score=0.95,
                enabled=True
            ),
            DataSourceConfig(
                source_id="alpha_vantage",
                source_type="MARKET_DATA",
                endpoint="https://www.alphavantage.co/query",
                api_key="demo",  # Replace with real API key
                rate_limit=500,
                reliability_score=0.90,
                enabled=True
            ),
            DataSourceConfig(
                source_id="polygon_io",
                source_type="MARKET_DATA",
                endpoint="https://api.polygon.io/v2/aggs/ticker/",
                api_key="demo",  # Replace with real API key
                rate_limit=1000,
                reliability_score=0.98,
                enabled=True
            ),
            
            # Cryptocurrency Data Sources
            DataSourceConfig(
                source_id="binance_api",
                source_type="CRYPTO_DATA",
                endpoint="https://api.binance.com/api/v3/ticker/price",
                rate_limit=1200,
                reliability_score=0.97,
                enabled=True
            ),
            DataSourceConfig(
                source_id="coinbase_api",
                source_type="CRYPTO_DATA",
                endpoint="https://api.coinbase.com/v2/exchange-rates",
                rate_limit=10000,
                reliability_score=0.93,
                enabled=True
            ),
            
            # Alternative Data Sources
            DataSourceConfig(
                source_id="news_api",
                source_type="NEWS_DATA",
                endpoint="https://newsapi.org/v2/everything",
                api_key="demo",  # Replace with real API key
                rate_limit=1000,
                reliability_score=0.85,
                enabled=True
            ),
            DataSourceConfig(
                source_id="twitter_api",
                source_type="SOCIAL_DATA",
                endpoint="https://api.twitter.com/2/tweets/search/recent",
                api_key="demo",  # Replace with real API key
                rate_limit=300,
                reliability_score=0.80,
                enabled=True
            ),
            DataSourceConfig(
                source_id="fred_economic",
                source_type="ECONOMIC_DATA",
                endpoint="https://api.stlouisfed.org/fred/series/observations",
                api_key="demo",  # Replace with real API key
                rate_limit=120,
                reliability_score=0.99,
                enabled=True
            ),
            DataSourceConfig(
                source_id="earnings_calendar",
                source_type="EARNINGS_DATA",
                endpoint="https://financialmodelingprep.com/api/v3/earning_calendar",
                api_key="demo",  # Replace with real API key
                rate_limit=250,
                reliability_score=0.88,
                enabled=True
            ),
            
            # Options and Derivatives Data
            DataSourceConfig(
                source_id="options_flow",
                source_type="OPTIONS_DATA",
                endpoint="https://api.unusualwhales.com/api/options_flow",
                api_key="demo",  # Replace with real API key
                rate_limit=100,
                reliability_score=0.92,
                enabled=True
            )
        ]
        
        for config in data_sources_config:
            self.data_sources[config.source_id] = config
            self.source_health[config.source_id] = 1.0
            self._store_data_source(config)
    
    def _store_data_source(self, source: DataSourceConfig):
        """Store data source configuration"""
        conn = sqlite3.connect(str(self.data_db_path))
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO data_sources 
            (source_id, source_type, endpoint, api_key, rate_limit, reliability_score, enabled, last_updated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            source.source_id,
            source.source_type,
            source.endpoint,
            source.api_key,
            source.rate_limit,
            source.reliability_score,
            source.enabled,
            datetime.now()
        ))
        
        conn.commit()
        conn.close()
    
    async def start_enhanced_data_feeds(self, symbols: List[str]) -> Dict[str, Any]:
        """Start enhanced data feeds for specified symbols"""
        print(f"\n📊 STARTING ENHANCED DATA FEEDS")
        print(f"   📈 Symbols: {symbols}")
        print(f"   🔗 Active sources: {len([s for s in self.data_sources.values() if s.enabled])}")
        
        # Start data collection tasks
        data_tasks = []
        
        # Market data collection
        for source_id, source in self.data_sources.items():
            if source.enabled and source.source_type in ["MARKET_DATA", "CRYPTO_DATA"]:
                task = asyncio.create_task(
                    self._collect_market_data(source, symbols)
                )
                data_tasks.append(task)
        
        # Alternative data collection
        for source_id, source in self.data_sources.items():
            if source.enabled and source.source_type in ["NEWS_DATA", "SOCIAL_DATA", "ECONOMIC_DATA"]:
                task = asyncio.create_task(
                    self._collect_alternative_data(source, symbols)
                )
                data_tasks.append(task)
        
        # Data quality monitoring
        quality_task = asyncio.create_task(self._monitor_data_quality())
        data_tasks.append(quality_task)
        
        print(f"   🚀 Started {len(data_tasks)} data collection tasks")
        
        return {
            'status': 'started',
            'symbols': symbols,
            'active_sources': len([s for s in self.data_sources.values() if s.enabled]),
            'tasks_started': len(data_tasks)
        }
    
    async def _collect_market_data(self, source: DataSourceConfig, symbols: List[str]):
        """Collect market data from a specific source"""
        while True:
            try:
                for symbol in symbols:
                    data_point = await self._fetch_market_data_point(source, symbol)
                    if data_point:
                        # Store in cache
                        self.market_data_cache[symbol].append(data_point)
                        
                        # Store in database
                        self._store_market_data_point(data_point)
                        
                        # Update quality metrics
                        self.data_quality_metrics['total_data_points'] += 1
                
                # Respect rate limits
                await asyncio.sleep(60 / source.rate_limit)
                
            except Exception as e:
                self.logger.error(f"Market data collection error for {source.source_id}: {e}")
                self.data_quality_metrics['failed_requests'] += 1
                self.source_health[source.source_id] *= 0.95  # Reduce health score
                await asyncio.sleep(60)  # Wait before retry
    
    async def _fetch_market_data_point(self, source: DataSourceConfig, symbol: str) -> Optional[MarketDataPoint]:
        """Fetch market data point from source"""
        start_time = time.time()
        
        try:
            if source.source_id == "yahoo_finance":
                return await self._fetch_yahoo_data(symbol)
            elif source.source_id == "alpha_vantage":
                return await self._fetch_alpha_vantage_data(source, symbol)
            elif source.source_id == "polygon_io":
                return await self._fetch_polygon_data(source, symbol)
            elif source.source_id == "binance_api":
                return await self._fetch_binance_data(symbol)
            elif source.source_id == "coinbase_api":
                return await self._fetch_coinbase_data(symbol)
            
        except Exception as e:
            self.logger.error(f"Failed to fetch data from {source.source_id} for {symbol}: {e}")
            return None
        finally:
            # Update latency metrics
            latency = time.time() - start_time
            self.data_quality_metrics['latency_avg'] = (
                self.data_quality_metrics['latency_avg'] * 0.9 + latency * 0.1
            )
    
    async def _fetch_yahoo_data(self, symbol: str) -> Optional[MarketDataPoint]:
        """Fetch data from Yahoo Finance"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            current_price = info.get('currentPrice') or info.get('regularMarketPrice')
            if not current_price:
                return None
            
            return MarketDataPoint(
                symbol=symbol,
                timestamp=datetime.now(),
                price=float(current_price),
                volume=info.get('volume', 0),
                bid=info.get('bid'),
                ask=info.get('ask'),
                high=info.get('dayHigh'),
                low=info.get('dayLow'),
                open=info.get('regularMarketOpen'),
                source="yahoo_finance",
                quality_score=0.95
            )
            
        except Exception as e:
            self.logger.error(f"Yahoo Finance error for {symbol}: {e}")
            return None
    
    async def _fetch_alpha_vantage_data(self, source: DataSourceConfig, symbol: str) -> Optional[MarketDataPoint]:
        """Fetch data from Alpha Vantage"""
        try:
            async with aiohttp.ClientSession() as session:
                params = {
                    'function': 'GLOBAL_QUOTE',
                    'symbol': symbol,
                    'apikey': source.api_key
                }
                
                async with session.get(source.endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        quote = data.get('Global Quote', {})
                        
                        if quote:
                            return MarketDataPoint(
                                symbol=symbol,
                                timestamp=datetime.now(),
                                price=float(quote.get('05. price', 0)),
                                volume=int(quote.get('06. volume', 0)),
                                high=float(quote.get('03. high', 0)),
                                low=float(quote.get('04. low', 0)),
                                open=float(quote.get('02. open', 0)),
                                source="alpha_vantage",
                                quality_score=0.90
                            )
            
        except Exception as e:
            self.logger.error(f"Alpha Vantage error for {symbol}: {e}")
            return None
    
    async def _fetch_polygon_data(self, source: DataSourceConfig, symbol: str) -> Optional[MarketDataPoint]:
        """Fetch data from Polygon.io"""
        # Implementation would go here
        return None
    
    async def _fetch_binance_data(self, symbol: str) -> Optional[MarketDataPoint]:
        """Fetch data from Binance"""
        try:
            # Convert symbol format for Binance (e.g., BTC-USD -> BTCUSDT)
            binance_symbol = symbol.replace('-', '').replace('USD', 'USDT')
            
            async with aiohttp.ClientSession() as session:
                url = f"https://api.binance.com/api/v3/ticker/price?symbol={binance_symbol}"
                
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        return MarketDataPoint(
                            symbol=symbol,
                            timestamp=datetime.now(),
                            price=float(data.get('price', 0)),
                            volume=0,  # Would need separate call for volume
                            source="binance_api",
                            quality_score=0.97
                        )
            
        except Exception as e:
            self.logger.error(f"Binance error for {symbol}: {e}")
            return None
    
    async def _fetch_coinbase_data(self, symbol: str) -> Optional[MarketDataPoint]:
        """Fetch data from Coinbase"""
        # Implementation would go here
        return None
    
    async def _collect_alternative_data(self, source: DataSourceConfig, symbols: List[str]):
        """Collect alternative data from a specific source"""
        while True:
            try:
                for symbol in symbols:
                    alt_data = await self._fetch_alternative_data(source, symbol)
                    if alt_data:
                        # Store in cache
                        self.alternative_data_cache[f"{symbol}_{source.source_type}"].append(alt_data)
                        
                        # Store in database
                        self._store_alternative_data(alt_data)
                
                # Respect rate limits (alternative data typically has lower frequency)
                await asyncio.sleep(300)  # 5 minutes between alternative data updates
                
            except Exception as e:
                self.logger.error(f"Alternative data collection error for {source.source_id}: {e}")
                await asyncio.sleep(600)  # Wait longer before retry
    
    async def _fetch_alternative_data(self, source: DataSourceConfig, symbol: str) -> Optional[AlternativeDataPoint]:
        """Fetch alternative data from source"""
        try:
            if source.source_id == "news_api":
                return await self._fetch_news_data(source, symbol)
            elif source.source_id == "twitter_api":
                return await self._fetch_social_data(source, symbol)
            elif source.source_id == "fred_economic":
                return await self._fetch_economic_data(source, symbol)
            
        except Exception as e:
            self.logger.error(f"Failed to fetch alternative data from {source.source_id}: {e}")
            return None
    
    async def _fetch_news_data(self, source: DataSourceConfig, symbol: str) -> Optional[AlternativeDataPoint]:
        """Fetch news data"""
        try:
            async with aiohttp.ClientSession() as session:
                params = {
                    'q': f"{symbol} stock",
                    'sortBy': 'publishedAt',
                    'apiKey': source.api_key,
                    'pageSize': 5
                }
                
                async with session.get(source.endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        articles = data.get('articles', [])
                        
                        if articles:
                            # Calculate sentiment score (simplified)
                            sentiment_score = self._calculate_news_sentiment(articles)
                            
                            return AlternativeDataPoint(
                                symbol=symbol,
                                data_type="NEWS",
                                timestamp=datetime.now(),
                                value=json.dumps(articles[:3]),  # Store top 3 articles
                                sentiment_score=sentiment_score,
                                confidence=0.75,
                                source="news_api"
                            )
            
        except Exception as e:
            self.logger.error(f"News API error for {symbol}: {e}")
            return None
    
    async def _fetch_social_data(self, source: DataSourceConfig, symbol: str) -> Optional[AlternativeDataPoint]:
        """Fetch social media data"""
        # Implementation would go here (requires Twitter API v2)
        return None
    
    async def _fetch_economic_data(self, source: DataSourceConfig, symbol: str) -> Optional[AlternativeDataPoint]:
        """Fetch economic data"""
        # Implementation would go here (FRED API)
        return None
    
    def _calculate_news_sentiment(self, articles: List[Dict]) -> float:
        """Calculate sentiment score from news articles (simplified)"""
        positive_words = ['gain', 'rise', 'up', 'bull', 'positive', 'growth', 'strong']
        negative_words = ['fall', 'drop', 'down', 'bear', 'negative', 'decline', 'weak']
        
        total_score = 0
        total_articles = len(articles)
        
        for article in articles:
            title = article.get('title', '').lower()
            description = article.get('description', '').lower()
            text = f"{title} {description}"
            
            positive_count = sum(1 for word in positive_words if word in text)
            negative_count = sum(1 for word in negative_words if word in text)
            
            if positive_count + negative_count > 0:
                article_sentiment = (positive_count - negative_count) / (positive_count + negative_count)
                total_score += article_sentiment
        
        return total_score / total_articles if total_articles > 0 else 0.0
    
    async def _monitor_data_quality(self):
        """Monitor data quality and source health"""
        while True:
            try:
                # Update source health scores
                for source_id in self.data_sources.keys():
                    # Check if source is responding
                    health_score = await self._check_source_health(source_id)
                    self.source_health[source_id] = health_score
                    
                    # Store quality metrics
                    self._store_quality_metric(source_id, 'health_score', health_score)
                
                # Update data freshness metrics
                for symbol in self.market_data_cache.keys():
                    if self.market_data_cache[symbol]:
                        latest_data = self.market_data_cache[symbol][-1]
                        freshness = (datetime.now() - latest_data.timestamp).total_seconds()
                        self.data_quality_metrics['data_freshness'][symbol] = freshness
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Data quality monitoring error: {e}")
                await asyncio.sleep(300)
    
    async def _check_source_health(self, source_id: str) -> float:
        """Check health of a data source"""
        source = self.data_sources.get(source_id)
        if not source or not source.enabled:
            return 0.0
        
        try:
            # Simple health check - try to connect to endpoint
            async with aiohttp.ClientSession() as session:
                async with session.get(source.endpoint, timeout=10) as response:
                    if response.status < 500:
                        return 1.0
                    else:
                        return 0.5
        except:
            return 0.0
    
    def _store_market_data_point(self, data_point: MarketDataPoint):
        """Store market data point in database"""
        conn = sqlite3.connect(str(self.data_db_path))
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO market_data 
            (symbol, timestamp, price, volume, bid, ask, high, low, open, source, quality_score)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data_point.symbol,
            data_point.timestamp,
            data_point.price,
            data_point.volume,
            data_point.bid,
            data_point.ask,
            data_point.high,
            data_point.low,
            data_point.open,
            data_point.source,
            data_point.quality_score
        ))
        
        conn.commit()
        conn.close()
    
    def _store_alternative_data(self, alt_data: AlternativeDataPoint):
        """Store alternative data in database"""
        conn = sqlite3.connect(str(self.data_db_path))
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO alternative_data 
            (symbol, data_type, timestamp, value, sentiment_score, confidence, source)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            alt_data.symbol,
            alt_data.data_type,
            alt_data.timestamp,
            json.dumps(alt_data.value) if not isinstance(alt_data.value, str) else alt_data.value,
            alt_data.sentiment_score,
            alt_data.confidence,
            alt_data.source
        ))
        
        conn.commit()
        conn.close()
    
    def _store_quality_metric(self, source_id: str, metric_type: str, metric_value: float):
        """Store data quality metric"""
        conn = sqlite3.connect(str(self.data_db_path))
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO data_quality_metrics (source_id, metric_type, metric_value, timestamp)
            VALUES (?, ?, ?, ?)
        ''', (source_id, metric_type, metric_value, datetime.now()))
        
        conn.commit()
        conn.close()
    
    def get_latest_market_data(self, symbol: str) -> Optional[MarketDataPoint]:
        """Get latest market data for symbol"""
        if symbol in self.market_data_cache and self.market_data_cache[symbol]:
            return self.market_data_cache[symbol][-1]
        return None
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """Get comprehensive data quality report"""
        return {
            'total_data_points': self.data_quality_metrics['total_data_points'],
            'failed_requests': self.data_quality_metrics['failed_requests'],
            'success_rate': (
                (self.data_quality_metrics['total_data_points'] - self.data_quality_metrics['failed_requests']) /
                max(self.data_quality_metrics['total_data_points'], 1)
            ),
            'average_latency': self.data_quality_metrics['latency_avg'],
            'source_health': self.source_health,
            'data_freshness': self.data_quality_metrics['data_freshness'],
            'active_sources': len([s for s in self.data_sources.values() if s.enabled]),
            'cached_symbols': len(self.market_data_cache)
        }

if __name__ == "__main__":
    async def main():
        data_system = EnhancedMarketDataSystem()
        
        # Start data feeds for test symbols
        symbols = ["AAPL", "MSFT", "GOOGL", "BTC-USD", "ETH-USD"]
        result = await data_system.start_enhanced_data_feeds(symbols)
        
        print(f"\n📊 ENHANCED DATA FEEDS STARTED")
        print(f"Status: {result['status']}")
        print(f"Active Sources: {result['active_sources']}")
        
        # Let it run for a bit to collect data
        await asyncio.sleep(30)
        
        # Get quality report
        quality_report = data_system.get_data_quality_report()
        print(f"\n📈 DATA QUALITY REPORT:")
        print(f"Success Rate: {quality_report['success_rate']:.2%}")
        print(f"Average Latency: {quality_report['average_latency']:.3f}s")
        print(f"Active Sources: {quality_report['active_sources']}")
    
    asyncio.run(main())
