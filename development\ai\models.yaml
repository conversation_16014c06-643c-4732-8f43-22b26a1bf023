api_models:
- local
cache_enabled: true
cache_ttl: 3600
confidence_threshold: 0.7
cost_tracking_enabled: true
daily_cost_limit: 50.0
enable_fine_tuning: true
ensemble_voting: true
fallback_order:
- local
llm_providers:
  anthropic:
    api_key: null
    base_url: null
    context_window: 4096
    cost_per_1k_tokens:
      input: 0.003
      output: 0.015
    enabled: false
    gpu_enabled: true
    inference_url: null
    max_tokens: 1000
    model: claude-3-sonnet-20240229
    model_path: null
    priority: 1
    provider_type: !!python/object/apply:core.config.config_manager.LLMProviderType
    - anthropic
    quantization: null
    rate_limit:
      requests_per_minute: 60
    retry_attempts: 3
    supports_function_calling: false
    supports_streaming: true
    temperature: 0.1
    timeout: 30
  deepseek:
    api_key: null
    base_url: null
    context_window: 4096
    cost_per_1k_tokens:
      input: 0.0014
      output: 0.0028
    enabled: false
    gpu_enabled: true
    inference_url: null
    max_tokens: 1000
    model: deepseek-chat
    model_path: null
    priority: 1
    provider_type: !!python/object/apply:core.config.config_manager.LLMProviderType
    - deepseek
    quantization: null
    rate_limit:
      requests_per_minute: 60
    retry_attempts: 3
    supports_function_calling: false
    supports_streaming: true
    temperature: 0.1
    timeout: 30
  local:
    api_key: null
    base_url: null
    context_window: 4096
    cost_per_1k_tokens:
      input: 0.0
      output: 0.0
    enabled: true
    gpu_enabled: false
    inference_url: null
    max_tokens: 2000
    model: qwen3-8b
    model_path: qwen3/Qwen3-8B-Q4_K_M.gguf
    priority: 1
    provider_type: !!python/object/apply:core.config.config_manager.LLMProviderType
    - qwen_local
    quantization: Q4_K_M
    rate_limit:
      requests_per_minute: 60
    retry_attempts: 3
    supports_function_calling: false
    supports_streaming: true
    temperature: 0.1
    timeout: 15
  openai:
    api_key: null
    base_url: null
    context_window: 4096
    cost_per_1k_tokens:
      input: 0.01
      output: 0.03
    enabled: false
    gpu_enabled: true
    inference_url: null
    max_tokens: 1000
    model: gpt-4-turbo-preview
    model_path: null
    priority: 1
    provider_type: !!python/object/apply:core.config.config_manager.LLMProviderType
    - openai
    quantization: null
    rate_limit:
      requests_per_minute: 60
    retry_attempts: 3
    supports_function_calling: false
    supports_streaming: true
    temperature: 0.1
    timeout: 30
local_model_name: qwen3-8b
local_model_path: qwen3/Qwen3-8B-Q4_K_M.gguf
max_api_calls_per_hour: 100
max_concurrent_requests: 10
request_timeout: 300
