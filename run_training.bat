@echo off
set SSL_CERT_FILE=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\certifi\cacert.pem
set REQUESTS_CA_BUNDLE=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\certifi\cacert.pem
set CURL_CA_BUNDLE=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\certifi\cacert.pem
echo Starting model training with SSL certificate fix...
python train_all_models.py --parallel --gpu-count 2
echo Training completed.
pause