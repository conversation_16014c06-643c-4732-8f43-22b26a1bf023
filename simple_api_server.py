#!/usr/bin/env python3
"""
SIMPLE API SERVER
Basic API server for system health monitoring and operations
"""

import json
import logging
import sqlite3
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
import socket
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

class SimpleAPIHandler(BaseHTTPRequestHandler):
    """Simple HTTP request handler"""
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_path = urllib.parse.urlparse(self.path)
            path = parsed_path.path
            
            if path == '/health':
                self._handle_health_check()
            elif path == '/status':
                self._handle_status()
            elif path == '/models':
                self._handle_models()
            elif path == '/databases':
                self._handle_databases()
            elif path == '/':
                self._handle_root()
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self._send_error(500, f"Internal Server Error: {e}")
    
    def _handle_health_check(self):
        """Handle health check endpoint"""
        health_data = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0',
            'uptime': time.time() - getattr(self.server, 'start_time', time.time()),
            'services': {
                'api_server': 'running',
                'database': self._check_database_health(),
                'ollama': self._check_ollama_health()
            }
        }
        
        self._send_json_response(health_data)
    
    def _handle_status(self):
        """Handle system status endpoint"""
        status_data = {
            'system': {
                'python_version': f"{__import__('sys').version_info.major}.{__import__('sys').version_info.minor}",
                'platform': __import__('platform').system(),
                'cpu_count': __import__('os').cpu_count(),
                'memory_available': self._get_memory_info(),
                'disk_space': self._get_disk_info()
            },
            'databases': self._get_database_info(),
            'ai_models': self._get_model_info(),
            'timestamp': datetime.now().isoformat()
        }
        
        self._send_json_response(status_data)
    
    def _handle_models(self):
        """Handle AI models endpoint"""
        models_data = {
            'total_models': 0,
            'models': [],
            'ollama_status': self._check_ollama_health(),
            'model_files': len(list(Path('.').glob('Modelfile.*')))
        }
        
        # Try to get Ollama models
        try:
            import urllib.request
            req = urllib.request.Request("http://localhost:11434/api/tags")
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    data = json.loads(response.read().decode())
                    models = data.get('models', [])
                    models_data['total_models'] = len(models)
                    models_data['models'] = [m.get('name', 'Unknown') for m in models[:10]]
        except Exception as e:
            models_data['error'] = str(e)
        
        self._send_json_response(models_data)
    
    def _handle_databases(self):
        """Handle databases endpoint"""
        db_files = list(Path('.').glob('*.db'))
        databases_data = {
            'total_databases': len(db_files),
            'databases': [],
            'total_size': 0
        }
        
        for db_file in db_files:
            try:
                size = db_file.stat().st_size
                databases_data['total_size'] += size
                
                # Test connectivity
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                conn.close()
                
                databases_data['databases'].append({
                    'name': db_file.name,
                    'size': size,
                    'tables': table_count,
                    'status': 'accessible'
                })
            except Exception as e:
                databases_data['databases'].append({
                    'name': db_file.name,
                    'size': 0,
                    'tables': 0,
                    'status': f'error: {e}'
                })
        
        self._send_json_response(databases_data)
    
    def _handle_root(self):
        """Handle root endpoint"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Noryon AI Trading System</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
                .endpoint { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .status { color: #27ae60; font-weight: bold; }
                a { color: #3498db; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Noryon AI Trading System</h1>
                <p class="status">✅ API Server Running</p>
                
                <h2>Available Endpoints:</h2>
                <div class="endpoint">
                    <strong>GET <a href="/health">/health</a></strong> - System health check
                </div>
                <div class="endpoint">
                    <strong>GET <a href="/status">/status</a></strong> - Detailed system status
                </div>
                <div class="endpoint">
                    <strong>GET <a href="/models">/models</a></strong> - AI models information
                </div>
                <div class="endpoint">
                    <strong>GET <a href="/databases">/databases</a></strong> - Database information
                </div>
                
                <h2>System Information:</h2>
                <p><strong>Timestamp:</strong> """ + datetime.now().isoformat() + """</p>
                <p><strong>Version:</strong> 1.0.0</p>
                <p><strong>Status:</strong> <span class="status">Operational</span></p>
            </div>
        </body>
        </html>
        """
        
        self._send_html_response(html_content)
    
    def _check_database_health(self) -> str:
        """Check database health"""
        try:
            db_files = list(Path('.').glob('*.db'))
            if not db_files:
                return 'no_databases'
            
            # Test first database
            conn = sqlite3.connect(str(db_files[0]))
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            conn.close()
            return 'healthy'
        except Exception:
            return 'error'
    
    def _check_ollama_health(self) -> str:
        """Check Ollama service health"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', 11434))
            sock.close()
            return 'running' if result == 0 else 'offline'
        except Exception:
            return 'error'
    
    def _get_memory_info(self) -> str:
        """Get memory information"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return f"{memory.available // (1024**3)}GB available"
        except ImportError:
            return "psutil not available"
        except Exception:
            return "unknown"
    
    def _get_disk_info(self) -> str:
        """Get disk space information"""
        try:
            import shutil
            total, used, free = shutil.disk_usage('.')
            free_gb = free // (1024**3)
            return f"{free_gb}GB free"
        except Exception:
            return "unknown"
    
    def _get_database_info(self) -> Dict[str, Any]:
        """Get database information"""
        db_files = list(Path('.').glob('*.db'))
        return {
            'count': len(db_files),
            'total_size': sum(f.stat().st_size for f in db_files),
            'status': 'healthy' if db_files else 'no_databases'
        }
    
    def _get_model_info(self) -> Dict[str, Any]:
        """Get AI model information"""
        model_files = list(Path('.').glob('Modelfile.*'))
        return {
            'model_files': len(model_files),
            'ollama_status': self._check_ollama_health()
        }
    
    def _send_json_response(self, data: Dict[str, Any]):
        """Send JSON response"""
        response_data = json.dumps(data, indent=2, default=str)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_data)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        self.wfile.write(response_data.encode('utf-8'))
    
    def _send_html_response(self, html: str):
        """Send HTML response"""
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.send_header('Content-Length', str(len(html)))
        self.end_headers()
        
        self.wfile.write(html.encode('utf-8'))
    
    def _send_error(self, code: int, message: str):
        """Send error response"""
        error_data = {
            'error': {
                'code': code,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }
        }
        
        response_data = json.dumps(error_data, indent=2)
        
        self.send_response(code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_data)))
        self.end_headers()
        
        self.wfile.write(response_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to use custom logging"""
        logging.info(f"{self.address_string()} - {format % args}")

class SimpleAPIServer:
    """Simple API Server"""
    
    def __init__(self, host='localhost', port=8000):
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """Start the API server"""
        try:
            self.server = HTTPServer((self.host, self.port), SimpleAPIHandler)
            self.server.start_time = time.time()
            
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            self.running = True
            self.logger.info(f"🚀 Simple API Server started on http://{self.host}:{self.port}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start API server: {e}")
            return False
    
    def stop(self):
        """Stop the API server"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.running = False
            self.logger.info("🛑 Simple API Server stopped")
    
    def is_running(self) -> bool:
        """Check if server is running"""
        return self.running and self.server_thread and self.server_thread.is_alive()

def main():
    """Main function to run the API server"""
    print("🚀 SIMPLE API SERVER")
    print("=" * 30)
    
    server = SimpleAPIServer()
    
    try:
        if server.start():
            print(f"✅ Server running on http://localhost:8000")
            print(f"📊 Health check: http://localhost:8000/health")
            print(f"🌐 Web interface: http://localhost:8000/")
            print("\nPress Ctrl+C to stop the server...")
            
            # Keep the main thread alive
            while server.is_running():
                time.sleep(1)
        else:
            print("❌ Failed to start server")
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down server...")
        server.stop()
        print("✅ Server stopped")

if __name__ == "__main__":
    main()
