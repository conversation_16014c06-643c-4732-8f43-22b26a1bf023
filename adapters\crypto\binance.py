import asyncio
import json
import hmac
import hashlib
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import aiohttp
import websockets
import logging

from ...core.interfaces.broker_interface import (
    UniversalBrokerInterface, UniversalTick, UniversalOrder, OrderResult,
    Position, AccountInfo, MarketData, HistoricalData, DataStream,
    OrderSide, OrderType, OrderStatus, TimeInForce, AssetType,
    BrokerError, ConnectionError, AuthenticationError, InsufficientFundsError,
    InvalidSymbolError, RateLimitError, OrderError
)

logger = logging.getLogger(__name__)

class BinanceDataStream(DataStream):
    """Binance-specific data stream implementation"""
    
    def __init__(self, symbols: List[str], callback: Callable[[UniversalTick], None], ws_url: str):
        super().__init__(symbols, callback)
        self.ws_url = ws_url
        self.websocket = None
        self.stream_names = []
    
    async def start(self):
        """Start the WebSocket connection"""
        self.is_active = True
        
        # Create stream names for Binance format
        self.stream_names = [f"{symbol.lower()}@ticker" for symbol in self.symbols]
        stream_url = f"{self.ws_url}/ws/{'/'.join(self.stream_names)}"
        
        try:
            self.websocket = await websockets.connect(stream_url)
            await self._listen()
        except Exception as e:
            logger.error(f"Failed to start Binance data stream: {e}")
            self.is_active = False
    
    async def _listen(self):
        """Listen for incoming data"""
        try:
            async for message in self.websocket:
                if not self.is_active:
                    break
                
                data = json.loads(message)
                tick = self._parse_ticker_data(data)
                if tick:
                    self.callback(tick)
        except websockets.exceptions.ConnectionClosed:
            logger.info("Binance WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error in Binance data stream: {e}")
    
    def _parse_ticker_data(self, data: Dict[str, Any]) -> Optional[UniversalTick]:
        """Parse Binance ticker data to UniversalTick"""
        try:
            return UniversalTick(
                symbol=data['s'],
                timestamp=datetime.fromtimestamp(data['E'] / 1000),
                bid=float(data['b']),
                ask=float(data['a']),
                last=float(data['c']),
                volume=float(data['v']),
                asset_type=AssetType.CRYPTO,
                broker_name="binance",
                broker_specific=data
            )
        except (KeyError, ValueError) as e:
            logger.error(f"Failed to parse Binance ticker data: {e}")
            return None
    
    async def stop(self):
        """Stop the data stream"""
        self.is_active = False
        if self.websocket:
            await self.websocket.close()

class BinanceAdapter(UniversalBrokerInterface):
    """Binance exchange adapter"""
    
    BROKER_NAME = "binance"
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config['broker']['connection']['base_url']
        self.ws_url = config['broker']['connection']['ws_url']
        self.api_key = None
        self.secret_key = None
        self.session = None
        self.recv_window = 5000
    
    async def connect(self, credentials: Dict[str, str]) -> bool:
        """Connect to Binance API"""
        try:
            self.api_key = credentials.get('api_key')
            self.secret_key = credentials.get('secret_key')
            
            if not self.api_key or not self.secret_key:
                raise AuthenticationError("API key and secret key required", self.broker_name)
            
            # Create HTTP session
            self.session = aiohttp.ClientSession()
            
            # Test connection
            await self._test_connection()
            
            self.is_connected = True
            logger.info("Connected to Binance successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Binance: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self) -> bool:
        """Disconnect from Binance API"""
        try:
            if self.session:
                await self.session.close()
            self.is_connected = False
            logger.info("Disconnected from Binance")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from Binance: {e}")
            return False
    
    async def _test_connection(self) -> None:
        """Test API connection"""
        endpoint = "/api/v3/account"
        await self._make_signed_request('GET', endpoint)
    
    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    async def _make_signed_request(self, method: str, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make a signed request to Binance API"""
        if not self.session:
            raise ConnectionError("Not connected to Binance", self.broker_name)
        
        params = params or {}
        params['timestamp'] = int(time.time() * 1000)
        params['recvWindow'] = self.recv_window
        
        # Create query string
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        signature = self._generate_signature(query_string)
        
        # Add signature
        query_string += f"&signature={signature}"
        
        headers = {
            'X-MBX-APIKEY': self.api_key
        }
        
        url = f"{self.base_url}{endpoint}?{query_string}"
        
        try:
            async with self.session.request(method, url, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    raise RateLimitError("Rate limit exceeded", self.broker_name)
                else:
                    error_text = await response.text()
                    raise BrokerError(f"API error: {error_text}", self.broker_name, str(response.status))
        
        except aiohttp.ClientError as e:
            raise ConnectionError(f"Network error: {e}", self.broker_name)
    
    async def _make_public_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make a public (unsigned) request"""
        if not self.session:
            raise ConnectionError("Not connected to Binance", self.broker_name)
        
        url = f"{self.base_url}{endpoint}"
        if params:
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            url += f"?{query_string}"
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise BrokerError(f"API error: {error_text}", self.broker_name, str(response.status))
        
        except aiohttp.ClientError as e:
            raise ConnectionError(f"Network error: {e}", self.broker_name)
    
    async def get_account_info(self) -> AccountInfo:
        """Get account information"""
        data = await self._make_signed_request('GET', '/api/v3/account')
        
        # Calculate total balance in USDT equivalent
        total_balance = 0
        for balance in data['balances']:
            if float(balance['free']) > 0 or float(balance['locked']) > 0:
                # For simplicity, assume USDT = 1, others would need price conversion
                if balance['asset'] == 'USDT':
                    total_balance += float(balance['free']) + float(balance['locked'])
        
        return AccountInfo(
            account_id=str(data.get('accountType', 'SPOT')),
            balance=total_balance,
            equity=total_balance,
            margin_used=0,  # Spot trading doesn't use margin
            margin_available=total_balance,
            currency='USDT',
            broker_name=self.broker_name,
            timestamp=datetime.utcnow(),
            broker_specific=data
        )
    
    async def get_positions(self) -> List[Position]:
        """Get all positions (balances for spot trading)"""
        account_data = await self._make_signed_request('GET', '/api/v3/account')
        positions = []
        
        for balance in account_data['balances']:
            total_balance = float(balance['free']) + float(balance['locked'])
            if total_balance > 0:
                # Get current price for the asset
                try:
                    if balance['asset'] != 'USDT':
                        symbol = f"{balance['asset']}USDT"
                        market_data = await self.get_market_data(symbol)
                        current_price = market_data.last
                    else:
                        current_price = 1.0
                    
                    position = Position(
                        symbol=balance['asset'],
                        quantity=total_balance,
                        average_price=current_price,  # Simplified
                        current_price=current_price,
                        unrealized_pnl=0,  # Would need historical data to calculate
                        realized_pnl=0,
                        asset_type=AssetType.CRYPTO,
                        broker_name=self.broker_name,
                        timestamp=datetime.utcnow(),
                        broker_specific=balance
                    )
                    positions.append(position)
                
                except Exception as e:
                    logger.warning(f"Could not get price for {balance['asset']}: {e}")
        
        return positions
    
    async def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for specific symbol"""
        positions = await self.get_positions()
        for position in positions:
            if position.symbol == symbol:
                return position
        return None
    
    async def get_market_data(self, symbol: str) -> MarketData:
        """Get current market data"""
        # Get 24hr ticker statistics
        data = await self._make_public_request('/api/v3/ticker/24hr', {'symbol': symbol})
        
        return MarketData(
            symbol=symbol,
            bid=float(data['bidPrice']),
            ask=float(data['askPrice']),
            last=float(data['lastPrice']),
            volume=float(data['volume']),
            high_24h=float(data['highPrice']),
            low_24h=float(data['lowPrice']),
            change_24h=float(data['priceChangePercent']),
            timestamp=datetime.utcnow(),
            broker_specific=data
        )
    
    async def get_historical_data(self, symbol: str, timeframe: str, start_time: datetime, end_time: datetime) -> HistoricalData:
        """Get historical kline data"""
        # Convert timeframe to Binance format
        interval_map = {
            '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
            '1h': '1h', '4h': '4h', '1d': '1d', '1w': '1w'
        }
        
        interval = interval_map.get(timeframe, '1h')
        
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': int(start_time.timestamp() * 1000),
            'endTime': int(end_time.timestamp() * 1000),
            'limit': 1000
        }
        
        data = await self._make_public_request('/api/v3/klines', params)
        
        # Convert to OHLCV format
        ohlcv_data = []
        for kline in data:
            ohlcv_data.append({
                'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5])
            })
        
        return HistoricalData(
            symbol=symbol,
            timeframe=timeframe,
            data=ohlcv_data,
            start_time=start_time,
            end_time=end_time,
            broker_name=self.broker_name
        )
    
    async def place_order(self, order: UniversalOrder) -> OrderResult:
        """Place a new order"""
        if not order.validate():
            return OrderResult(success=False, error_message="Invalid order parameters")
        
        # Convert to Binance format
        params = {
            'symbol': order.symbol,
            'side': order.side.value.upper(),
            'type': self._convert_order_type(order.type),
            'quantity': order.quantity
        }
        
        # Add price for limit orders
        if order.type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
            params['price'] = order.price
        
        # Add stop price for stop orders
        if order.type in [OrderType.STOP, OrderType.STOP_LIMIT]:
            params['stopPrice'] = order.stop_price
        
        # Add time in force
        if order.time_in_force != TimeInForce.GTC:
            params['timeInForce'] = order.time_in_force.value.upper()
        
        try:
            result = await self._make_signed_request('POST', '/api/v3/order', params)
            
            return OrderResult(
                success=True,
                order_id=str(result['orderId']),
                broker_response=result,
                timestamp=datetime.utcnow()
            )
        
        except BrokerError as e:
            error_message = str(e)
            if "insufficient balance" in error_message.lower():
                raise InsufficientFundsError(error_message, self.broker_name)
            elif "invalid symbol" in error_message.lower():
                raise InvalidSymbolError(error_message, self.broker_name)
            else:
                raise OrderError(error_message, self.broker_name)
    
    def _convert_order_type(self, order_type: OrderType) -> str:
        """Convert universal order type to Binance format"""
        mapping = {
            OrderType.MARKET: 'MARKET',
            OrderType.LIMIT: 'LIMIT',
            OrderType.STOP: 'STOP_LOSS',
            OrderType.STOP_LIMIT: 'STOP_LOSS_LIMIT'
        }
        return mapping.get(order_type, 'MARKET')
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order"""
        try:
            # Note: Binance requires symbol to cancel order
            # In a real implementation, you'd need to track orders or get symbol from order_id
            params = {'orderId': order_id}
            await self._make_signed_request('DELETE', '/api/v3/order', params)
            return True
        except Exception as e:
            logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    async def get_order_status(self, order_id: str) -> OrderStatus:
        """Get status of an order"""
        try:
            # Note: Binance requires symbol to get order status
            # In a real implementation, you'd need to track orders
            params = {'orderId': order_id}
            result = await self._make_signed_request('GET', '/api/v3/order', params)
            
            status_mapping = {
                'NEW': OrderStatus.PENDING,
                'PARTIALLY_FILLED': OrderStatus.PARTIALLY_FILLED,
                'FILLED': OrderStatus.FILLED,
                'CANCELED': OrderStatus.CANCELLED,
                'REJECTED': OrderStatus.REJECTED
            }
            
            return status_mapping.get(result['status'], OrderStatus.PENDING)
        
        except Exception as e:
            logger.error(f"Failed to get order status for {order_id}: {e}")
            return OrderStatus.PENDING
    
    async def get_open_orders(self) -> List[Dict[str, Any]]:
        """Get all open orders"""
        try:
            result = await self._make_signed_request('GET', '/api/v3/openOrders')
            return result
        except Exception as e:
            logger.error(f"Failed to get open orders: {e}")
            return []
    
    async def subscribe_to_data(self, symbols: List[str], callback: Callable[[UniversalTick], None]) -> DataStream:
        """Subscribe to real-time data"""
        stream = BinanceDataStream(symbols, callback, self.ws_url)
        # Start the stream in the background
        asyncio.create_task(stream.start())
        return stream
    
    async def get_supported_symbols(self) -> List[str]:
        """Get list of supported trading symbols"""
        try:
            data = await self._make_public_request('/api/v3/exchangeInfo')
            symbols = []
            for symbol_info in data['symbols']:
                if symbol_info['status'] == 'TRADING':
                    symbols.append(symbol_info['symbol'])
            return symbols
        except Exception as e:
            logger.error(f"Failed to get supported symbols: {e}")
            return []
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for Binance (remove separators)"""
        return symbol.replace('/', '').replace('-', '').upper()
    
    def denormalize_symbol(self, symbol: str) -> str:
        """Convert Binance symbol to universal format"""
        # This is simplified - in reality you'd need a mapping
        if symbol.endswith('USDT'):
            base = symbol[:-4]
            return f"{base}/USDT"
        elif symbol.endswith('BTC'):
            base = symbol[:-3]
            return f"{base}/BTC"
        return symbol