#!/usr/bin/env python3
"""
Comprehensive Live Trading Dashboard
Real-time monitoring of the complete AI trading system
"""

import asyncio
import sqlite3
import json
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
from rich.layout import Layout
from rich.columns import Columns
from rich.progress import Progress, BarColumn, TextColumn, TimeRemainingColumn

console = Console()

class ComprehensiveLiveDashboard:
    """Real-time comprehensive trading system dashboard"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.refresh_interval = 2  # seconds
        
        # System metrics
        self.system_metrics = {
            'total_models': 0,
            'active_models': 0,
            'paper_trading_active': False,
            'total_trades': 0,
            'current_pnl': 0.0,
            'portfolio_value': 100000.0,
            'win_rate': 0.0,
            'system_uptime': 0.0,
            'analysis_time': 0.0,
            'ensemble_confidence': 0.0
        }
        
        # Database connections
        self.databases = {
            'paper_trading': 'paper_trading_simplified.db',
            'ai_models': 'ai_model_integration.db',
            'ensemble': 'ensemble_voting_system.db',
            'performance': 'performance_analytics.db'
        }
        
        console.print(Panel(
            f"[bold green]📊 COMPREHENSIVE LIVE DASHBOARD INITIALIZED[/bold green]\n\n"
            f"Monitoring: AI Models, Paper Trading, Ensemble Voting, Performance\n"
            f"Refresh Rate: {self.refresh_interval}s\n"
            f"Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Status: ACTIVE",
            title="Live Dashboard"
        ))
    
    def create_layout(self) -> Layout:
        """Create dashboard layout"""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        layout["left"].split_column(
            Layout(name="portfolio", size=12),
            Layout(name="trades", size=12)
        )
        
        layout["right"].split_column(
            Layout(name="models", size=12),
            Layout(name="performance", size=12)
        )
        
        return layout
    
    def get_paper_trading_data(self) -> Dict[str, Any]:
        """Get paper trading data"""
        try:
            if os.path.exists(self.databases['paper_trading']):
                conn = sqlite3.connect(self.databases['paper_trading'])
                
                # Get portfolio summary
                cursor = conn.execute('''
                    SELECT COUNT(*) as open_positions,
                           SUM(pnl) as total_pnl,
                           AVG(pnl_percent) as avg_pnl_percent
                    FROM positions WHERE status = 'OPEN' OR status IS NULL
                ''')
                portfolio_data = cursor.fetchone()
                
                # Get recent trades
                cursor = conn.execute('''
                    SELECT symbol, action, quantity, price, pnl, timestamp
                    FROM trades ORDER BY timestamp DESC LIMIT 5
                ''')
                recent_trades = cursor.fetchall()
                
                # Get performance metrics
                cursor = conn.execute('''
                    SELECT COUNT(*) as total_trades,
                           SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as winning_trades
                    FROM trades
                ''')
                performance_data = cursor.fetchone()
                
                conn.close()
                
                total_trades = performance_data[0] if performance_data[0] else 0
                winning_trades = performance_data[1] if performance_data[1] else 0
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
                
                return {
                    'open_positions': portfolio_data[0] if portfolio_data[0] else 0,
                    'total_pnl': portfolio_data[1] if portfolio_data[1] else 0.0,
                    'avg_pnl_percent': portfolio_data[2] if portfolio_data[2] else 0.0,
                    'recent_trades': recent_trades,
                    'total_trades': total_trades,
                    'win_rate': win_rate,
                    'active': True
                }
            else:
                return {'active': False}
        except Exception as e:
            return {'active': False, 'error': str(e)}
    
    def get_ai_models_data(self) -> Dict[str, Any]:
        """Get AI models data"""
        try:
            if os.path.exists(self.databases['ai_models']):
                conn = sqlite3.connect(self.databases['ai_models'])
                
                # Get model summary
                cursor = conn.execute('''
                    SELECT COUNT(*) as total_models,
                           COUNT(CASE WHEN status = 'AVAILABLE' THEN 1 END) as active_models,
                           AVG(performance_score) as avg_performance,
                           COUNT(CASE WHEN specialization = 'finance' THEN 1 END) as finance_models
                    FROM models
                ''')
                model_data = cursor.fetchone()
                
                # Get top performing models
                cursor = conn.execute('''
                    SELECT name, specialization, performance_score, confidence_threshold
                    FROM models ORDER BY performance_score DESC LIMIT 5
                ''')
                top_models = cursor.fetchall()
                
                conn.close()
                
                return {
                    'total_models': model_data[0] if model_data[0] else 0,
                    'active_models': model_data[1] if model_data[1] else 0,
                    'avg_performance': model_data[2] if model_data[2] else 0.0,
                    'finance_models': model_data[3] if model_data[3] else 0,
                    'top_models': top_models,
                    'available': True
                }
            else:
                return {'available': False}
        except Exception as e:
            return {'available': False, 'error': str(e)}
    
    def get_system_performance(self) -> Dict[str, Any]:
        """Get system performance metrics"""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        # Simulate performance metrics (in real implementation, these would come from actual monitoring)
        return {
            'uptime_seconds': uptime,
            'uptime_formatted': str(timedelta(seconds=int(uptime))),
            'analysis_time': 0.1,  # Simulated
            'memory_usage': 85.2,  # Simulated
            'cpu_usage': 45.8,     # Simulated
            'ensemble_confidence': 0.82,  # Simulated
            'system_health': 'EXCELLENT'
        }
    
    def update_dashboard(self, layout: Layout):
        """Update dashboard with latest data"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Get data
        paper_data = self.get_paper_trading_data()
        models_data = self.get_ai_models_data()
        performance_data = self.get_system_performance()
        
        # Header
        layout["header"].update(Panel(
            f"[bold blue]🚀 NORYON AI TRADING SYSTEM - LIVE DASHBOARD[/bold blue] | "
            f"Time: {current_time} | "
            f"Uptime: {performance_data['uptime_formatted']} | "
            f"Status: [green]OPERATIONAL[/green]",
            style="blue"
        ))
        
        # Portfolio Status
        if paper_data.get('active', False):
            portfolio_table = Table(title="📊 Portfolio Status", show_header=True)
            portfolio_table.add_column("Metric", style="cyan")
            portfolio_table.add_column("Value", style="green")
            
            total_portfolio = 100000 + paper_data.get('total_pnl', 0)
            pnl_color = "green" if paper_data.get('total_pnl', 0) >= 0 else "red"
            
            portfolio_table.add_row("Portfolio Value", f"${total_portfolio:,.2f}")
            portfolio_table.add_row("Total P&L", f"[{pnl_color}]${paper_data.get('total_pnl', 0):.2f}[/{pnl_color}]")
            portfolio_table.add_row("Open Positions", str(paper_data.get('open_positions', 0)))
            portfolio_table.add_row("Total Trades", str(paper_data.get('total_trades', 0)))
            portfolio_table.add_row("Win Rate", f"{paper_data.get('win_rate', 0):.1f}%")
            portfolio_table.add_row("Avg P&L %", f"{paper_data.get('avg_pnl_percent', 0):.2f}%")
        else:
            portfolio_table = Panel("[yellow]Paper Trading: INACTIVE[/yellow]", title="📊 Portfolio Status")
        
        layout["portfolio"].update(portfolio_table)
        
        # Recent Trades
        if paper_data.get('active', False) and paper_data.get('recent_trades'):
            trades_table = Table(title="📈 Recent Trades", show_header=True)
            trades_table.add_column("Symbol", style="cyan")
            trades_table.add_column("Action", style="yellow")
            trades_table.add_column("Quantity", style="blue")
            trades_table.add_column("Price", style="magenta")
            trades_table.add_column("P&L", style="green")
            
            for trade in paper_data['recent_trades']:
                pnl_color = "green" if trade[4] >= 0 else "red"
                trades_table.add_row(
                    trade[0],  # symbol
                    trade[1],  # action
                    f"{trade[2]:.2f}",  # quantity
                    f"${trade[3]:.2f}",  # price
                    f"[{pnl_color}]${trade[4]:.2f}[/{pnl_color}]"  # pnl
                )
        else:
            trades_table = Panel("[yellow]No recent trades[/yellow]", title="📈 Recent Trades")
        
        layout["trades"].update(trades_table)
        
        # AI Models Status
        if models_data.get('available', False):
            models_table = Table(title="🤖 AI Models Status", show_header=True)
            models_table.add_column("Model", style="cyan")
            models_table.add_column("Specialization", style="yellow")
            models_table.add_column("Performance", style="green")
            models_table.add_column("Confidence", style="blue")
            
            for model in models_data.get('top_models', [])[:5]:
                models_table.add_row(
                    model[0][:25] + "..." if len(model[0]) > 25 else model[0],
                    model[1],
                    f"{model[2]:.3f}",
                    f"{model[3]:.2f}"
                )
            
            # Add summary row
            models_summary = Panel(
                f"Total Models: {models_data.get('total_models', 0)}\n"
                f"Active Models: {models_data.get('active_models', 0)}\n"
                f"Finance Models: {models_data.get('finance_models', 0)}\n"
                f"Avg Performance: {models_data.get('avg_performance', 0):.3f}",
                title="Models Summary"
            )
        else:
            models_table = Panel("[yellow]AI Models: LOADING[/yellow]", title="🤖 AI Models Status")
            models_summary = Panel("[yellow]Models data unavailable[/yellow]", title="Models Summary")
        
        layout["models"].update(Columns([models_table, models_summary]))
        
        # System Performance
        performance_table = Table(title="⚡ System Performance", show_header=True)
        performance_table.add_column("Metric", style="cyan")
        performance_table.add_column("Value", style="green")
        performance_table.add_column("Status", style="yellow")
        
        # Analysis time status
        analysis_status = "🟢 EXCELLENT" if performance_data['analysis_time'] < 1.0 else "🟡 GOOD"
        memory_status = "🟢 GOOD" if performance_data['memory_usage'] < 90 else "🟡 HIGH"
        cpu_status = "🟢 NORMAL" if performance_data['cpu_usage'] < 70 else "🟡 HIGH"
        
        performance_table.add_row("Analysis Time", f"{performance_data['analysis_time']:.1f}s", analysis_status)
        performance_table.add_row("Memory Usage", f"{performance_data['memory_usage']:.1f}%", memory_status)
        performance_table.add_row("CPU Usage", f"{performance_data['cpu_usage']:.1f}%", cpu_status)
        performance_table.add_row("Ensemble Confidence", f"{performance_data['ensemble_confidence']:.2f}", "🟢 HIGH")
        performance_table.add_row("System Health", performance_data['system_health'], "🟢 EXCELLENT")
        
        layout["performance"].update(performance_table)
        
        # Footer
        footer_text = (
            f"[green]🟢 Paper Trading: {'ACTIVE' if paper_data.get('active') else 'INACTIVE'}[/green] | "
            f"[blue]🤖 AI Models: {models_data.get('active_models', 0)}/{models_data.get('total_models', 0)}[/blue] | "
            f"[yellow]⚡ Performance: {performance_data['system_health']}[/yellow] | "
            f"[magenta]📊 Refresh: {self.refresh_interval}s[/magenta]"
        )
        
        layout["footer"].update(Panel(footer_text, style="green"))
    
    async def run_dashboard(self, duration_minutes: int = 60):
        """Run the live dashboard"""
        layout = self.create_layout()
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        console.print(f"[green]🚀 Starting live dashboard for {duration_minutes} minutes...[/green]")
        
        with Live(layout, refresh_per_second=0.5, screen=True):
            try:
                while datetime.now() < end_time:
                    self.update_dashboard(layout)
                    await asyncio.sleep(self.refresh_interval)
            except KeyboardInterrupt:
                console.print("\n[yellow]Dashboard stopped by user[/yellow]")
        
        console.print(Panel(
            f"[bold green]📊 DASHBOARD SESSION COMPLETED[/bold green]\n\n"
            f"Duration: {duration_minutes} minutes\n"
            f"Total Uptime: {(datetime.now() - self.start_time).total_seconds()/60:.1f} minutes\n"
            f"Status: SUCCESSFUL",
            title="Session Complete"
        ))

async def main():
    """Main dashboard function"""
    console.print("[bold blue]🚀 COMPREHENSIVE LIVE TRADING DASHBOARD[/bold blue]\n")
    
    # Initialize dashboard
    dashboard = ComprehensiveLiveDashboard()
    
    # Run dashboard for 10 minutes (or until interrupted)
    await dashboard.run_dashboard(duration_minutes=10)

if __name__ == "__main__":
    asyncio.run(main())
