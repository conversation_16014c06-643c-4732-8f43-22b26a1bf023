# Noryon LLM-Brain Trading System Environment Configuration
# Generated on 2025-06-01 15:17:18

# =============================================================================
# LLM API CONFIGURATION
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# DeepSeek Configuration
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# Anthropic Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key-here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Qwen Configuration
QWEN_API_KEY=your-qwen-api-key-here
QWEN_MODEL=qwen-turbo
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# Local LLM Configuration (Optional)
LOCAL_LLM_ENABLED=false
LOCAL_LLM_URL=http://localhost:11434
LOCAL_LLM_MODEL=llama2

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=noryon_llm_brain
POSTGRES_USER=noryon_user
POSTGRES_PASSWORD=uwGV$EgBIvx9^Y*EpG!&KFVtVOO*#g8w

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=nAC2Ka#c1$p*hLJ%rWqgKS74wzt^1RhN
REDIS_DB=0

# =============================================================================
# BROKER API CONFIGURATION
# =============================================================================
# Binance Configuration
BINANCE_API_KEY=your-binance-api-key-here
BINANCE_SECRET_KEY=your-binance-secret-key-here
BINANCE_TESTNET=true
BINANCE_BASE_URL=https://testnet.binance.vision

# Coinbase Pro Configuration
COINBASE_API_KEY=your-coinbase-api-key-here
COINBASE_SECRET_KEY=your-coinbase-secret-key-here
COINBASE_PASSPHRASE=your-coinbase-passphrase-here
COINBASE_SANDBOX=true

# OANDA Configuration
OANDA_API_KEY=your-oanda-api-key-here
OANDA_ACCOUNT_ID=your-oanda-account-id-here
OANDA_ENVIRONMENT=practice
OANDA_BASE_URL=https://api-fxpractice.oanda.com

# Interactive Brokers Configuration
IB_HOST=localhost
IB_PORT=7497
IB_CLIENT_ID=1
IB_ACCOUNT=your-ib-account-here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration
JWT_SECRET_KEY=!X5dt&@W6QziVmIlM7vdxsE10kGnsYFBolAbS3AKh^fER6X1uO0Yykd3V2eFe$ka
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# API Security
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=3600
API_CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password-here
SMTP_USE_TLS=true

# Slack Configuration
SLACK_WEBHOOK_URL=your-slack-webhook-url-here
SLACK_CHANNEL=#trading-alerts

# Discord Configuration
DISCORD_WEBHOOK_URL=your-discord-webhook-url-here

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
TELEGRAM_CHAT_ID=your-telegram-chat-id-here

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================
# Application Settings
APP_NAME=Noryon LLM-Brain Trading System
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
APP_DEBUG=true
APP_LOG_LEVEL=DEBUG

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_WORKERS=4
SERVER_TIMEOUT=300

# Data Configuration
DATA_RETENTION_DAYS=90
BACKUP_RETENTION_DAYS=30
LOG_RETENTION_DAYS=7

# Performance Configuration
MAX_CONCURRENT_DECISIONS=10
DECISION_TIMEOUT_SECONDS=30
LLM_REQUEST_TIMEOUT=60
CACHE_TTL_SECONDS=300

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Prometheus Configuration
PROMETHEUS_PORT=9091
PROMETHEUS_SCRAPE_INTERVAL=15s

# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin
GRAFANA_PORT=3000

# Elasticsearch Configuration
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX=noryon-logs

# Kibana Configuration
KIBANA_PORT=5601

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
# Risk Management
MAX_POSITION_SIZE=0.1
MAX_DAILY_LOSS=0.02
MAX_DRAWDOWN=0.15
EMERGENCY_LIQUIDATION_THRESHOLD=0.08

# Trading Parameters
MIN_TRADE_SIZE_USD=10.0
MAX_TRADE_SIZE_USD=10000.0
DEFAULT_LEVERAGE=1.0
SLIPPAGE_TOLERANCE=0.001

# Asset Configuration
ENABLED_CRYPTO_PAIRS=BTCUSD,ETHUSD,ADAUSD,SOLUSD
ENABLED_FOREX_PAIRS=EURUSD,GBPUSD,USDJPY,AUDUSD
ENABLED_STOCK_SYMBOLS=AAPL,GOOGL,MSFT,TSLA

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_CRYPTO_TRADING=true
ENABLE_FOREX_TRADING=true
ENABLE_STOCK_TRADING=false
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_NEWS_ANALYSIS=true
ENABLE_SOCIAL_MEDIA_ANALYSIS=false
ENABLE_BACKTESTING=true
ENABLE_PAPER_TRADING=true
ENABLE_LIVE_TRADING=false
