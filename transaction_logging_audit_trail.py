#!/usr/bin/env python3
"""
TRANSACTION LOGGING AND AUDIT TRAIL SYSTEM
Complete decision audit trails, immutable audit logs for regulatory compliance,
transaction traceability from market data input to final trading decision.
"""

import sqlite3
import hashlib
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import uuid
from pathlib import Path
import threading
from cryptography.fernet import Fernet
import base64

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AuditLogEntry:
    """Immutable audit log entry"""
    entry_id: str
    transaction_id: str
    event_type: str
    event_data: Dict[str, Any]
    user_id: str
    session_id: str
    timestamp: datetime
    hash_value: str
    previous_hash: str

@dataclass
class TransactionTrace:
    """Complete transaction traceability record"""
    transaction_id: str
    symbol: str
    
    # Input data
    market_data_input: Dict[str, Any]
    technical_indicators: Dict[str, Any]
    ai_model_inputs: List[Dict[str, Any]]
    
    # Processing steps
    validation_steps: List[Dict[str, Any]]
    risk_assessment_steps: List[Dict[str, Any]]
    fallback_steps: List[Dict[str, Any]]
    
    # Decision process
    model_decisions: List[Dict[str, Any]]
    ensemble_voting: Dict[str, Any]
    final_decision: Dict[str, Any]
    
    # Execution
    execution_steps: List[Dict[str, Any]]
    trade_execution: Optional[Dict[str, Any]]
    
    # Metadata
    total_processing_time: float
    models_used: List[str]
    confidence_scores: List[float]
    risk_scores: List[float]
    
    # Audit trail
    created_by: str
    created_at: datetime
    last_updated: datetime

class TransactionLoggingAuditTrail:
    """
    Comprehensive transaction logging and audit trail system
    Provides immutable audit logs and complete transaction traceability
    """
    
    def __init__(self, db_path: str = "transaction_audit.db", encryption_key: Optional[bytes] = None):
        self.db_path = db_path
        self.setup_database()
        
        # Encryption for sensitive data
        if encryption_key:
            self.cipher = Fernet(encryption_key)
        else:
            # Generate a key for this session (in production, use secure key management)
            self.cipher = Fernet(Fernet.generate_key())
        
        # Audit chain tracking
        self.last_hash = "0" * 64  # Genesis hash
        self.audit_lock = threading.Lock()
        
        # Transaction tracking
        self.active_transactions = {}
        self.transaction_stats = {
            'total_transactions': 0,
            'completed_transactions': 0,
            'failed_transactions': 0,
            'avg_processing_time': 0.0,
            'total_audit_entries': 0
        }
        
        # Compliance configuration
        self.compliance_config = {
            'retention_period_days': 2555,  # 7 years for financial records
            'encryption_required': True,
            'immutable_logs': True,
            'real_time_monitoring': True,
            'regulatory_reporting': True
        }
        
        logger.info("📋 Transaction Logging and Audit Trail System initialized")
        logger.info(f"   🗃️ Database: {self.db_path}")
        logger.info(f"   🔐 Encryption: {'Enabled' if encryption_key else 'Session key generated'}")
        logger.info(f"   📊 Retention period: {self.compliance_config['retention_period_days']} days")
    
    def setup_database(self):
        """Setup audit trail database with immutable log structure"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Immutable audit log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY,
                entry_id TEXT UNIQUE,
                transaction_id TEXT,
                event_type TEXT,
                event_data TEXT,
                user_id TEXT,
                session_id TEXT,
                timestamp DATETIME,
                hash_value TEXT,
                previous_hash TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Transaction traces table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transaction_traces (
                id INTEGER PRIMARY KEY,
                transaction_id TEXT UNIQUE,
                symbol TEXT,
                market_data_input TEXT,
                technical_indicators TEXT,
                ai_model_inputs TEXT,
                validation_steps TEXT,
                risk_assessment_steps TEXT,
                fallback_steps TEXT,
                model_decisions TEXT,
                ensemble_voting TEXT,
                final_decision TEXT,
                execution_steps TEXT,
                trade_execution TEXT,
                total_processing_time REAL,
                models_used TEXT,
                confidence_scores TEXT,
                risk_scores TEXT,
                created_by TEXT,
                created_at DATETIME,
                last_updated DATETIME
            )
        ''')
        
        # Decision audit table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS decision_audit (
                id INTEGER PRIMARY KEY,
                decision_id TEXT,
                transaction_id TEXT,
                model_name TEXT,
                input_data TEXT,
                output_data TEXT,
                confidence_score REAL,
                processing_time REAL,
                validation_result TEXT,
                risk_assessment TEXT,
                timestamp DATETIME
            )
        ''')
        
        # Compliance events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS compliance_events (
                id INTEGER PRIMARY KEY,
                event_id TEXT,
                event_type TEXT,
                description TEXT,
                severity TEXT,
                transaction_id TEXT,
                user_id TEXT,
                remediation_required BOOLEAN,
                remediation_status TEXT,
                timestamp DATETIME
            )
        ''')
        
        # System events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_events (
                id INTEGER PRIMARY KEY,
                event_id TEXT,
                event_type TEXT,
                component TEXT,
                event_data TEXT,
                severity TEXT,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("✅ Transaction audit database initialized")
    
    def start_transaction_trace(self, symbol: str, market_data: Dict[str, Any], 
                              user_id: str = "system", session_id: Optional[str] = None) -> str:
        """Start a new transaction trace"""
        
        transaction_id = f"TXN_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        if not session_id:
            session_id = f"SES_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        # Create transaction trace
        trace = TransactionTrace(
            transaction_id=transaction_id,
            symbol=symbol,
            market_data_input=market_data,
            technical_indicators={},
            ai_model_inputs=[],
            validation_steps=[],
            risk_assessment_steps=[],
            fallback_steps=[],
            model_decisions=[],
            ensemble_voting={},
            final_decision={},
            execution_steps=[],
            trade_execution=None,
            total_processing_time=0.0,
            models_used=[],
            confidence_scores=[],
            risk_scores=[],
            created_by=user_id,
            created_at=datetime.now(),
            last_updated=datetime.now()
        )
        
        # Store in active transactions
        self.active_transactions[transaction_id] = {
            'trace': trace,
            'start_time': time.time(),
            'user_id': user_id,
            'session_id': session_id
        }
        
        # Log transaction start
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="TRANSACTION_STARTED",
            event_data={
                'symbol': symbol,
                'market_data_keys': list(market_data.keys()),
                'user_id': user_id
            },
            user_id=user_id,
            session_id=session_id
        )
        
        self.transaction_stats['total_transactions'] += 1
        
        logger.info(f"📋 Transaction trace started: {transaction_id} for {symbol}")
        
        return transaction_id
    
    def log_technical_analysis(self, transaction_id: str, indicators: Dict[str, Any]):
        """Log technical analysis step"""
        
        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return
        
        trace = self.active_transactions[transaction_id]['trace']
        trace.technical_indicators = indicators
        trace.last_updated = datetime.now()
        
        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="TECHNICAL_ANALYSIS",
            event_data={
                'indicators_count': len(indicators),
                'indicators': list(indicators.keys())
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )

    def log_model_decision(self, transaction_id: str, model_name: str, decision: Dict[str, Any],
                         processing_time: float):
        """Log AI model decision"""

        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return

        trace = self.active_transactions[transaction_id]['trace']

        model_decision = {
            'model_name': model_name,
            'decision': decision,
            'processing_time': processing_time,
            'timestamp': datetime.now()
        }

        trace.model_decisions.append(model_decision)
        trace.models_used.append(model_name)
        trace.confidence_scores.append(decision.get('confidence', 0.0))
        trace.last_updated = datetime.now()

        # Log to decision audit table
        self._log_decision_audit(transaction_id, model_name, decision, processing_time)

        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="MODEL_DECISION",
            event_data={
                'model_name': model_name,
                'action': decision.get('action', 'unknown'),
                'confidence': decision.get('confidence', 0.0),
                'processing_time': processing_time
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )

    def log_ensemble_voting(self, transaction_id: str, voting_result: Dict[str, Any]):
        """Log ensemble voting result"""

        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return

        trace = self.active_transactions[transaction_id]['trace']
        trace.ensemble_voting = voting_result
        trace.last_updated = datetime.now()

        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="ENSEMBLE_VOTING",
            event_data={
                'final_action': voting_result.get('action', 'unknown'),
                'consensus_confidence': voting_result.get('confidence', 0.0),
                'models_voted': len(voting_result.get('model_votes', []))
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )

    def log_final_decision(self, transaction_id: str, final_decision: Dict[str, Any]):
        """Log final trading decision"""

        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return

        trace = self.active_transactions[transaction_id]['trace']
        trace.final_decision = final_decision
        trace.last_updated = datetime.now()

        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="FINAL_DECISION",
            event_data={
                'action': final_decision.get('action', 'unknown'),
                'confidence': final_decision.get('confidence', 0.0),
                'position_size': final_decision.get('position_size', 0.0),
                'risk_level': final_decision.get('risk_level', 'unknown')
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )

    def log_trade_execution(self, transaction_id: str, execution_result: Dict[str, Any]):
        """Log trade execution result"""

        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return

        trace = self.active_transactions[transaction_id]['trace']
        trace.trade_execution = execution_result
        trace.last_updated = datetime.now()

        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="TRADE_EXECUTION",
            event_data={
                'execution_status': execution_result.get('status', 'unknown'),
                'executed_price': execution_result.get('executed_price', 0.0),
                'executed_quantity': execution_result.get('executed_quantity', 0.0),
                'execution_time': execution_result.get('execution_time', 0.0)
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )

    def complete_transaction_trace(self, transaction_id: str) -> TransactionTrace:
        """Complete and store transaction trace"""

        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return None

        transaction_data = self.active_transactions[transaction_id]
        trace = transaction_data['trace']

        # Calculate total processing time
        trace.total_processing_time = time.time() - transaction_data['start_time']
        trace.last_updated = datetime.now()

        # Store complete trace
        self._store_transaction_trace(trace)

        # Log completion
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="TRANSACTION_COMPLETED",
            event_data={
                'total_processing_time': trace.total_processing_time,
                'models_used_count': len(trace.models_used),
                'validation_steps_count': len(trace.validation_steps),
                'final_action': trace.final_decision.get('action', 'unknown')
            },
            user_id=transaction_data['user_id'],
            session_id=transaction_data['session_id']
        )

        # Update statistics
        self.transaction_stats['completed_transactions'] += 1

        # Update average processing time
        total_completed = self.transaction_stats['completed_transactions']
        current_avg = self.transaction_stats['avg_processing_time']
        self.transaction_stats['avg_processing_time'] = (
            (current_avg * (total_completed - 1) + trace.total_processing_time) / total_completed
        )

        # Remove from active transactions
        del self.active_transactions[transaction_id]

        logger.info(f"📋 Transaction trace completed: {transaction_id} ({trace.total_processing_time:.2f}s)")

        return trace

    def log_audit_event(self, transaction_id: str, event_type: str, event_data: Dict[str, Any],
                       user_id: str, session_id: str):
        """Log immutable audit event"""

        with self.audit_lock:
            entry_id = f"AUD_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            timestamp = datetime.now()

            # Encrypt sensitive data if required
            if self.compliance_config['encryption_required']:
                encrypted_data = self.cipher.encrypt(json.dumps(event_data).encode())
                event_data_str = base64.b64encode(encrypted_data).decode()
            else:
                event_data_str = json.dumps(event_data)

            # Create hash for immutability
            hash_input = f"{entry_id}{transaction_id}{event_type}{event_data_str}{user_id}{session_id}{timestamp}{self.last_hash}"
            hash_value = hashlib.sha256(hash_input.encode()).hexdigest()

            # Create audit log entry
            audit_entry = AuditLogEntry(
                entry_id=entry_id,
                transaction_id=transaction_id,
                event_type=event_type,
                event_data=event_data,
                user_id=user_id,
                session_id=session_id,
                timestamp=timestamp,
                hash_value=hash_value,
                previous_hash=self.last_hash
            )

            # Store in database
            self._store_audit_entry(audit_entry, event_data_str)

            # Update last hash for chain integrity
            self.last_hash = hash_value

            # Update statistics
            self.transaction_stats['total_audit_entries'] += 1

    def _store_audit_entry(self, entry: AuditLogEntry, encrypted_data: str):
        """Store audit entry in database"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO audit_log
            (entry_id, transaction_id, event_type, event_data, user_id,
             session_id, timestamp, hash_value, previous_hash)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            entry.entry_id, entry.transaction_id, entry.event_type,
            encrypted_data, entry.user_id, entry.session_id,
            entry.timestamp, entry.hash_value, entry.previous_hash
        ))

        conn.commit()
        conn.close()

    def _store_transaction_trace(self, trace: TransactionTrace):
        """Store complete transaction trace"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO transaction_traces
            (transaction_id, symbol, market_data_input, technical_indicators,
             ai_model_inputs, validation_steps, risk_assessment_steps,
             fallback_steps, model_decisions, ensemble_voting, final_decision,
             execution_steps, trade_execution, total_processing_time,
             models_used, confidence_scores, risk_scores, created_by,
             created_at, last_updated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trace.transaction_id, trace.symbol,
            json.dumps(trace.market_data_input),
            json.dumps(trace.technical_indicators),
            json.dumps(trace.ai_model_inputs),
            json.dumps(trace.validation_steps),
            json.dumps(trace.risk_assessment_steps),
            json.dumps(trace.fallback_steps),
            json.dumps(trace.model_decisions),
            json.dumps(trace.ensemble_voting),
            json.dumps(trace.final_decision),
            json.dumps(trace.execution_steps),
            json.dumps(trace.trade_execution) if trace.trade_execution else None,
            trace.total_processing_time,
            json.dumps(trace.models_used),
            json.dumps(trace.confidence_scores),
            json.dumps(trace.risk_scores),
            trace.created_by, trace.created_at, trace.last_updated
        ))

        conn.commit()
        conn.close()

    def _log_decision_audit(self, transaction_id: str, model_name: str,
                          decision: Dict[str, Any], processing_time: float):
        """Log decision to audit table"""

        decision_id = f"DEC_{int(time.time())}_{uuid.uuid4().hex[:8]}"

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO decision_audit
            (decision_id, transaction_id, model_name, input_data, output_data,
             confidence_score, processing_time, validation_result,
             risk_assessment, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            decision_id, transaction_id, model_name,
            json.dumps({}),  # Input data would be stored here
            json.dumps(decision),
            decision.get('confidence', 0.0),
            processing_time,
            decision.get('validation_result', 'unknown'),
            decision.get('risk_assessment', 'unknown'),
            datetime.now()
        ))

        conn.commit()
        conn.close()

    def verify_audit_chain_integrity(self) -> Dict[str, Any]:
        """Verify the integrity of the audit chain"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT entry_id, transaction_id, event_type, event_data, user_id,
                   session_id, timestamp, hash_value, previous_hash
            FROM audit_log ORDER BY id
        ''')

        entries = cursor.fetchall()
        conn.close()

        integrity_check = {
            'total_entries': len(entries),
            'verified_entries': 0,
            'integrity_violations': [],
            'chain_valid': True
        }

        previous_hash = "0" * 64  # Genesis hash

        for entry in entries:
            entry_id, transaction_id, event_type, event_data, user_id, session_id, timestamp, hash_value, stored_previous_hash = entry

            # Verify previous hash chain
            if stored_previous_hash != previous_hash:
                integrity_check['integrity_violations'].append({
                    'entry_id': entry_id,
                    'violation_type': 'HASH_CHAIN_BROKEN',
                    'expected_previous_hash': previous_hash,
                    'actual_previous_hash': stored_previous_hash
                })
                integrity_check['chain_valid'] = False

            # Verify hash calculation
            hash_input = f"{entry_id}{transaction_id}{event_type}{event_data}{user_id}{session_id}{timestamp}{previous_hash}"
            calculated_hash = hashlib.sha256(hash_input.encode()).hexdigest()

            if calculated_hash != hash_value:
                integrity_check['integrity_violations'].append({
                    'entry_id': entry_id,
                    'violation_type': 'HASH_MISMATCH',
                    'expected_hash': calculated_hash,
                    'actual_hash': hash_value
                })
                integrity_check['chain_valid'] = False
            else:
                integrity_check['verified_entries'] += 1

            previous_hash = hash_value

        return integrity_check

    def get_transaction_trace(self, transaction_id: str) -> Optional[TransactionTrace]:
        """Retrieve complete transaction trace"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM transaction_traces WHERE transaction_id = ?
        ''', (transaction_id,))

        result = cursor.fetchone()
        conn.close()

        if not result:
            return None

        # Reconstruct TransactionTrace object
        trace = TransactionTrace(
            transaction_id=result[1],
            symbol=result[2],
            market_data_input=json.loads(result[3]),
            technical_indicators=json.loads(result[4]),
            ai_model_inputs=json.loads(result[5]),
            validation_steps=json.loads(result[6]),
            risk_assessment_steps=json.loads(result[7]),
            fallback_steps=json.loads(result[8]),
            model_decisions=json.loads(result[9]),
            ensemble_voting=json.loads(result[10]),
            final_decision=json.loads(result[11]),
            execution_steps=json.loads(result[12]),
            trade_execution=json.loads(result[13]) if result[13] else None,
            total_processing_time=result[14],
            models_used=json.loads(result[15]),
            confidence_scores=json.loads(result[16]),
            risk_scores=json.loads(result[17]),
            created_by=result[18],
            created_at=datetime.fromisoformat(result[19]),
            last_updated=datetime.fromisoformat(result[20])
        )

        return trace

    def generate_compliance_report(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate compliance report for regulatory requirements"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get transaction statistics
        cursor.execute('''
            SELECT COUNT(*) FROM transaction_traces
            WHERE created_at BETWEEN ? AND ?
        ''', (start_date, end_date))

        total_transactions = cursor.fetchone()[0]

        # Get audit log statistics
        cursor.execute('''
            SELECT COUNT(*) FROM audit_log
            WHERE timestamp BETWEEN ? AND ?
        ''', (start_date, end_date))

        total_audit_entries = cursor.fetchone()[0]

        # Get decision statistics
        cursor.execute('''
            SELECT model_name, COUNT(*), AVG(confidence_score), AVG(processing_time)
            FROM decision_audit
            WHERE timestamp BETWEEN ? AND ?
            GROUP BY model_name
        ''', (start_date, end_date))

        model_statistics = cursor.fetchall()

        conn.close()

        # Verify audit chain integrity
        integrity_check = self.verify_audit_chain_integrity()

        compliance_report = {
            'report_id': f"COMP_{int(time.time())}",
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'transaction_statistics': {
                'total_transactions': total_transactions,
                'total_audit_entries': total_audit_entries,
                'avg_processing_time': self.transaction_stats['avg_processing_time']
            },
            'model_statistics': [
                {
                    'model_name': stat[0],
                    'decision_count': stat[1],
                    'avg_confidence': stat[2],
                    'avg_processing_time': stat[3]
                }
                for stat in model_statistics
            ],
            'audit_integrity': integrity_check,
            'compliance_status': {
                'encryption_enabled': self.compliance_config['encryption_required'],
                'immutable_logs': self.compliance_config['immutable_logs'],
                'retention_compliant': True,  # Would check actual retention
                'audit_trail_complete': integrity_check['chain_valid']
            },
            'generated_at': datetime.now()
        }

        return compliance_report

    def get_audit_summary(self) -> Dict[str, Any]:
        """Get comprehensive audit system summary"""

        return {
            'transaction_stats': self.transaction_stats.copy(),
            'active_transactions': len(self.active_transactions),
            'compliance_config': self.compliance_config.copy(),
            'database_path': self.db_path,
            'encryption_enabled': bool(self.cipher),
            'last_hash': self.last_hash,
            'timestamp': datetime.now()
        }
    
    def log_ai_model_input(self, transaction_id: str, model_name: str, input_data: Dict[str, Any]):
        """Log AI model input"""
        
        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return
        
        trace = self.active_transactions[transaction_id]['trace']
        
        model_input = {
            'model_name': model_name,
            'input_data': input_data,
            'timestamp': datetime.now()
        }
        
        trace.ai_model_inputs.append(model_input)
        trace.last_updated = datetime.now()
        
        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="AI_MODEL_INPUT",
            event_data={
                'model_name': model_name,
                'input_keys': list(input_data.keys())
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )
    
    def log_validation_step(self, transaction_id: str, validation_type: str, 
                          validation_result: Dict[str, Any]):
        """Log validation step"""
        
        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return
        
        trace = self.active_transactions[transaction_id]['trace']
        
        validation_step = {
            'validation_type': validation_type,
            'result': validation_result,
            'timestamp': datetime.now()
        }
        
        trace.validation_steps.append(validation_step)
        trace.last_updated = datetime.now()
        
        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="VALIDATION_STEP",
            event_data={
                'validation_type': validation_type,
                'validation_result': validation_result.get('validation_result', 'unknown'),
                'confidence_score': validation_result.get('confidence_score', 0.0)
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )
    
    def log_risk_assessment_step(self, transaction_id: str, risk_type: str, 
                               risk_assessment: Dict[str, Any]):
        """Log risk assessment step"""
        
        if transaction_id not in self.active_transactions:
            logger.warning(f"Transaction {transaction_id} not found")
            return
        
        trace = self.active_transactions[transaction_id]['trace']
        
        risk_step = {
            'risk_type': risk_type,
            'assessment': risk_assessment,
            'timestamp': datetime.now()
        }
        
        trace.risk_assessment_steps.append(risk_step)
        trace.risk_scores.append(risk_assessment.get('risk_score', 0.0))
        trace.last_updated = datetime.now()
        
        # Log audit event
        self.log_audit_event(
            transaction_id=transaction_id,
            event_type="RISK_ASSESSMENT",
            event_data={
                'risk_type': risk_type,
                'risk_level': risk_assessment.get('risk_level', 'unknown'),
                'risk_score': risk_assessment.get('risk_score', 0.0)
            },
            user_id=self.active_transactions[transaction_id]['user_id'],
            session_id=self.active_transactions[transaction_id]['session_id']
        )
