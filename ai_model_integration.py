#!/usr/bin/env python3
"""
AI MODEL INTEGRATION
Complete integration of 30+ AI models with the NORYON trading system
"""

import subprocess
import json
import time
import asyncio
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional

class AIModelIntegrator:
    """Complete AI model integration system"""
    
    def __init__(self):
        self.ollama_host = "localhost"
        self.ollama_port = 11434
        self.available_models = {}
        self.model_hierarchy = {}
        self.confidence_thresholds = {}
        
        # Initialize integration database
        self._init_integration_database()
        
        print("🤖 AI Model Integrator initialized")
    
    def _init_integration_database(self):
        """Initialize AI model integration database"""
        try:
            conn = sqlite3.connect('ai_model_integration.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_registry (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT UNIQUE,
                    model_type TEXT,
                    specialization TEXT,
                    confidence_threshold REAL,
                    priority_level INTEGER,
                    status TEXT,
                    last_tested DATETIME,
                    performance_score REAL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_tests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT,
                    test_timestamp DATETIME,
                    test_type TEXT,
                    success BOOLEAN,
                    response_time REAL,
                    error_message TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ AI model integration database initialized")
            
        except Exception as e:
            print(f"❌ Failed to initialize integration database: {e}")
    
    def check_ollama_service(self) -> bool:
        """Check if Ollama service is running"""
        
        print(f"🔍 Checking Ollama service on {self.ollama_host}:{self.ollama_port}")
        
        try:
            # Try to connect to Ollama API
            result = subprocess.run([
                'curl', '-s', f'http://{self.ollama_host}:{self.ollama_port}/api/tags'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ Ollama service is running")
                return True
            else:
                print("⚠️ Ollama service not responding via curl, trying ollama command")
                
                # Try ollama list command
                result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print("✅ Ollama service accessible via CLI")
                    return True
                else:
                    print("❌ Ollama service not accessible")
                    return False
                    
        except FileNotFoundError:
            print("⚠️ Ollama not installed or not in PATH")
            return False
        except subprocess.TimeoutExpired:
            print("❌ Ollama service timeout")
            return False
        except Exception as e:
            print(f"❌ Error checking Ollama: {e}")
            return False
    
    def discover_available_models(self) -> Dict[str, Any]:
        """Discover all available AI models"""
        
        print("🔍 Discovering available AI models...")
        
        discovered_models = {}
        
        try:
            # Get models from Ollama
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 1:
                            model_name = parts[0]
                            
                            # Categorize model based on name
                            model_info = self._categorize_model(model_name)
                            discovered_models[model_name] = model_info
                            
                            print(f"   ✅ Found: {model_name} ({model_info['specialization']})")
            
            # Add your specific models if not found
            expected_models = [
                'deepseek-r1', 'gemma3:12b', 'phi4:9b', 'qwen3', 'mimo',
                'noryon-phi-4-9b-finance', 'noryon-gemma-3-12b-finance',
                'noryon-deepseek-r1-finance', 'noryon-qwen3-finance',
                'liberated_deepseek-r1_14b', 'unrestricted_phi4_14b'
            ]
            
            for model in expected_models:
                if model not in discovered_models:
                    # Check if model exists but wasn't listed
                    test_result = subprocess.run([
                        'ollama', 'show', model
                    ], capture_output=True, text=True, timeout=10)
                    
                    if test_result.returncode == 0:
                        model_info = self._categorize_model(model)
                        discovered_models[model] = model_info
                        print(f"   ✅ Found (via show): {model} ({model_info['specialization']})")
            
            self.available_models = discovered_models
            print(f"\n📊 Total models discovered: {len(discovered_models)}")
            
            return discovered_models
            
        except Exception as e:
            print(f"❌ Error discovering models: {e}")
            return {}
    
    def _categorize_model(self, model_name: str) -> Dict[str, Any]:
        """Categorize model based on name and assign properties"""
        
        model_name_lower = model_name.lower()
        
        # Determine specialization
        if 'finance' in model_name_lower or 'trading' in model_name_lower:
            specialization = 'finance_trading'
            priority = 1
            confidence_threshold = 0.7
        elif 'mimo' in model_name_lower:
            specialization = 'mathematical_reasoning'
            priority = 0  # Highest priority
            confidence_threshold = 0.75
        elif 'deepseek' in model_name_lower:
            specialization = 'code_reasoning'
            priority = 2
            confidence_threshold = 0.7
        elif 'phi' in model_name_lower:
            specialization = 'general_reasoning'
            priority = 2
            confidence_threshold = 0.65
        elif 'gemma' in model_name_lower:
            specialization = 'instruction_following'
            priority = 3
            confidence_threshold = 0.6
        elif 'qwen' in model_name_lower:
            specialization = 'multilingual_reasoning'
            priority = 3
            confidence_threshold = 0.6
        else:
            specialization = 'general_purpose'
            priority = 4
            confidence_threshold = 0.5
        
        # Determine model type
        if 'unrestricted' in model_name_lower or 'liberated' in model_name_lower:
            model_type = 'unrestricted'
        elif 'enhanced' in model_name_lower:
            model_type = 'enhanced'
        else:
            model_type = 'standard'
        
        return {
            'specialization': specialization,
            'model_type': model_type,
            'priority': priority,
            'confidence_threshold': confidence_threshold,
            'status': 'discovered'
        }
    
    def test_model_connectivity(self, model_name: str) -> Dict[str, Any]:
        """Test connectivity and performance of a specific model"""
        
        print(f"🧪 Testing model: {model_name}")
        
        test_start = time.time()
        test_result = {
            'model_name': model_name,
            'success': False,
            'response_time': 0.0,
            'error_message': None,
            'test_timestamp': datetime.now()
        }
        
        try:
            # Simple test prompt
            test_prompt = "Analyze AAPL stock: current price $150, volume 1M, trend bullish. Recommend BUY, SELL, or HOLD with confidence 0-1."
            
            # Test model response
            result = subprocess.run([
                'ollama', 'run', model_name, test_prompt
            ], capture_output=True, text=True, timeout=30)
            
            response_time = time.time() - test_start
            test_result['response_time'] = response_time
            
            if result.returncode == 0 and result.stdout.strip():
                test_result['success'] = True
                print(f"   ✅ {model_name}: Response in {response_time:.2f}s")
                
                # Basic response validation
                response = result.stdout.strip().lower()
                if any(word in response for word in ['buy', 'sell', 'hold']):
                    print(f"   ✅ {model_name}: Valid trading response")
                else:
                    print(f"   ⚠️ {model_name}: Response may need prompt tuning")
                    
            else:
                test_result['error_message'] = result.stderr or "No response"
                print(f"   ❌ {model_name}: Failed - {test_result['error_message'][:50]}")
                
        except subprocess.TimeoutExpired:
            test_result['error_message'] = "Timeout"
            test_result['response_time'] = 30.0
            print(f"   ❌ {model_name}: Timeout after 30s")
            
        except Exception as e:
            test_result['error_message'] = str(e)
            print(f"   ❌ {model_name}: Error - {str(e)[:50]}")
        
        # Log test result to database
        self._log_model_test(test_result)
        
        return test_result
    
    def _log_model_test(self, test_result: Dict[str, Any]):
        """Log model test result to database"""
        
        try:
            conn = sqlite3.connect('ai_model_integration.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO model_tests (
                    model_name, test_timestamp, test_type, success, 
                    response_time, error_message
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                test_result['model_name'],
                test_result['test_timestamp'],
                'connectivity',
                test_result['success'],
                test_result['response_time'],
                test_result['error_message']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ Failed to log test result: {e}")
    
    def configure_model_hierarchy(self) -> Dict[str, Any]:
        """Configure model priority hierarchy"""
        
        print("🏗️ Configuring model hierarchy...")
        
        hierarchy = {
            'primary_reasoning': [],      # Priority 0 - MiMo-7B
            'finance_specialists': [],    # Priority 1 - Finance models
            'reasoning_models': [],       # Priority 2 - DeepSeek, Phi
            'general_models': [],         # Priority 3 - Gemma, Qwen
            'fallback_models': []         # Priority 4+ - Others
        }
        
        for model_name, model_info in self.available_models.items():
            priority = model_info['priority']
            specialization = model_info['specialization']
            
            if priority == 0 or 'mimo' in model_name.lower():
                hierarchy['primary_reasoning'].append(model_name)
            elif priority == 1 or specialization == 'finance_trading':
                hierarchy['finance_specialists'].append(model_name)
            elif priority == 2 or specialization in ['code_reasoning', 'general_reasoning']:
                hierarchy['reasoning_models'].append(model_name)
            elif priority == 3:
                hierarchy['general_models'].append(model_name)
            else:
                hierarchy['fallback_models'].append(model_name)
        
        self.model_hierarchy = hierarchy
        
        print("✅ Model hierarchy configured:")
        for category, models in hierarchy.items():
            if models:
                print(f"   📊 {category}: {len(models)} models")
                for model in models[:3]:  # Show first 3
                    print(f"      - {model}")
                if len(models) > 3:
                    print(f"      ... and {len(models) - 3} more")
        
        return hierarchy
    
    def register_models_in_system(self):
        """Register all discovered models in the system database"""
        
        print("📝 Registering models in system...")
        
        try:
            conn = sqlite3.connect('ai_model_integration.db')
            cursor = conn.cursor()
            
            registered_count = 0
            
            for model_name, model_info in self.available_models.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO model_registry (
                        model_name, model_type, specialization, confidence_threshold,
                        priority_level, status, last_tested, performance_score
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    model_name,
                    model_info['model_type'],
                    model_info['specialization'],
                    model_info['confidence_threshold'],
                    model_info['priority'],
                    model_info['status'],
                    datetime.now(),
                    0.0  # Will be updated after testing
                ))
                
                registered_count += 1
            
            conn.commit()
            conn.close()
            
            print(f"✅ Registered {registered_count} models in system")
            
        except Exception as e:
            print(f"❌ Failed to register models: {e}")
    
    async def test_enhanced_fallback_integration(self):
        """Test integration with MiMoEnhancedFallbackSystem"""
        
        print("🔗 Testing enhanced fallback integration...")
        
        try:
            # Import the enhanced fallback system
            from mimo_integration import MiMoEnhancedFallbackSystem
            
            enhanced_fallback = MiMoEnhancedFallbackSystem()
            
            # Test with sample market data
            test_symbol = 'AAPL'
            test_context = {
                'current_price': 150.0,
                'volume': 1000000,
                'trend': 'bullish',
                'volatility': 0.2
            }
            
            print(f"🧪 Testing enhanced fallback with {test_symbol}...")
            
            start_time = time.time()
            decision = await enhanced_fallback.execute_with_mimo_fallback(test_symbol, test_context)
            execution_time = time.time() - start_time
            
            print(f"✅ Enhanced fallback test successful:")
            print(f"   Action: {decision['action']}")
            print(f"   Confidence: {decision['confidence']:.2f}")
            print(f"   Model Used: {decision['model_used']}")
            print(f"   Execution Time: {execution_time:.3f}s")
            print(f"   Success: {decision['success']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Enhanced fallback integration failed: {e}")
            return False
    
    def generate_integration_report(self) -> Dict[str, Any]:
        """Generate comprehensive integration report"""
        
        print("📋 Generating integration report...")
        
        # Count models by category
        total_models = len(self.available_models)
        working_models = sum(1 for model in self.available_models.values() 
                           if model.get('status') == 'working')
        
        # Get test results
        try:
            conn = sqlite3.connect('ai_model_integration.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT COUNT(*) FROM model_tests WHERE success = 1
            ''')
            successful_tests = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT COUNT(*) FROM model_tests
            ''')
            total_tests = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT AVG(response_time) FROM model_tests WHERE success = 1
            ''')
            avg_response_time = cursor.fetchone()[0] or 0.0
            
            conn.close()
            
        except Exception:
            successful_tests = 0
            total_tests = 0
            avg_response_time = 0.0
        
        report = {
            'integration_timestamp': datetime.now().isoformat(),
            'ollama_service_status': self.check_ollama_service(),
            'total_models_discovered': total_models,
            'working_models': working_models,
            'model_hierarchy': self.model_hierarchy,
            'test_results': {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': successful_tests / max(1, total_tests),
                'avg_response_time': avg_response_time
            },
            'integration_status': 'complete' if total_models > 0 else 'failed'
        }
        
        # Save report
        with open('ai_model_integration_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"✅ Integration report saved")
        
        return report

async def main():
    """Main integration function"""
    
    print("🚀 Starting AI Model Integration...")
    
    integrator = AIModelIntegrator()
    
    # Step 1: Check Ollama service
    ollama_running = integrator.check_ollama_service()
    
    if not ollama_running:
        print("⚠️ Ollama service not running. Please start Ollama first:")
        print("   Windows: Start Ollama Desktop application")
        print("   Linux/Mac: ollama serve")
        return False
    
    # Step 2: Discover models
    models = integrator.discover_available_models()
    
    if not models:
        print("⚠️ No models discovered. Please install models first:")
        print("   Example: ollama pull deepseek-r1")
        return False
    
    # Step 3: Test model connectivity
    print(f"\n🧪 Testing {len(models)} models...")
    working_models = 0
    
    for model_name in list(models.keys())[:10]:  # Test first 10 models
        test_result = integrator.test_model_connectivity(model_name)
        if test_result['success']:
            working_models += 1
            integrator.available_models[model_name]['status'] = 'working'
    
    # Step 4: Configure hierarchy
    hierarchy = integrator.configure_model_hierarchy()
    
    # Step 5: Register models
    integrator.register_models_in_system()
    
    # Step 6: Test enhanced fallback
    fallback_working = await integrator.test_enhanced_fallback_integration()
    
    # Step 7: Generate report
    report = integrator.generate_integration_report()
    
    # Final summary
    print(f"\n🎯 AI MODEL INTEGRATION COMPLETE")
    print(f"   📊 Models Discovered: {len(models)}")
    print(f"   ✅ Working Models: {working_models}")
    print(f"   🔗 Enhanced Fallback: {'✅ WORKING' if fallback_working else '❌ FAILED'}")
    print(f"   📋 Integration Status: {report['integration_status'].upper()}")
    
    success = len(models) > 0 and working_models > 0 and fallback_working
    
    if success:
        print(f"\n🎉 AI MODEL INTEGRATION SUCCESSFUL!")
        print(f"   🤖 Your 30+ AI models are now integrated with the trading system")
        print(f"   🧠 MiMo-7B configured as primary reasoning model")
        print(f"   📊 Model hierarchy established for optimal performance")
    else:
        print(f"\n⚠️ AI MODEL INTEGRATION PARTIAL")
        print(f"   🔧 Some models may need additional configuration")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
