ensemble:
  confidence_threshold: 0.7
  enabled: true
  models:
  - name: noryon-gemma-3-finance
    specialization: market_analysis
    type: ollama
    weight: 0.25
  - name: noryon-phi-4-finance
    specialization: risk_assessment
    type: ollama
    weight: 0.2
  - name: kernelllm
    specialization: gpu_optimization
    type: transformers
    weight: 0.15
    model_path: kernelllm
    capabilities:
      - triton_kernel_generation
      - gpu_optimization
      - performance_enhancement
      - pytorch_to_triton_conversion
    trading_applications:
      - technical_indicators
      - portfolio_calculations
      - risk_metrics
      - market_data_processing
  voting_strategy: weighted
